Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to sys.o(.text) for Stm32_Clock_Init
    main.o(.text) refers to delay.o(.text) for Delay_Init
    main.o(.text) refers to motor.o(.text) for Motor_Init
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to oled.o(.text) for OLED_Init
    main.o(.text) refers to usart.o(.text) for USART1_Init
    main.o(.text) refers to dma.o(.text) for MYDMA_Config
    main.o(.text) refers to control.o(.text) for PID_Init
    main.o(.text) refers to anbt_dmp_mpu6050.o(.text) for AnBT_DMP_MPU6050_Init
    main.o(.text) refers to data_transfer.o(.text) for Flash_Read_Language
    main.o(.text) refers to ui.o(.text) for Standby_Mode
    main.o(.text) refers to f2d.o(.text) for __aeabi_f2d
    main.o(.text) refers to cdcmple.o(.text) for __aeabi_cdcmple
    main.o(.text) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    main.o(.text) refers to data_transfer.o(.bss) for Blue_dat
    main.o(.text) refers to led.o(.data) for LED_state
    main.o(.text) refers to data_transfer.o(.data) for Blue_RC
    main.o(.text) refers to ui.o(.data) for WorkMode
    main.o(.text) refers to control.o(.data) for PickUp_Flag
    main.o(.text) refers to anbt_dmp_mpu6050.o(.data) for MPU6050_accel
    main.o(.text) refers to control.o(.bss) for PID_Speed
    main.o(.text) refers to main.o(.data) for RC_Last
    control.o(.text) refers to data_transfer.o(.text) for Flash_Read_PID
    control.o(.text) refers to fadd.o(.text) for __aeabi_frsub
    control.o(.text) refers to fmul.o(.text) for __aeabi_fmul
    control.o(.text) refers to anbt_dmp_mpu6050.o(.text) for number_to_dps
    control.o(.text) refers to fflti.o(.text) for __aeabi_i2f
    control.o(.text) refers to f2d.o(.text) for __aeabi_f2d
    control.o(.text) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    control.o(.text) refers to cdcmple.o(.text) for __aeabi_cdcmple
    control.o(.text) refers to ffixi.o(.text) for __aeabi_f2iz
    control.o(.text) refers to motor.o(.text) for Set_Motor
    control.o(.text) refers to control.o(.data) for Front_SafeAngle
    control.o(.text) refers to control.o(.bss) for PID_Speed
    control.o(.text) refers to anbt_dmp_mpu6050.o(.data) for MPU6050_gyro
    data_transfer.o(.text) refers to fmul.o(.text) for __aeabi_fmul
    data_transfer.o(.text) refers to ffixi.o(.text) for __aeabi_f2iz
    data_transfer.o(.text) refers to usart.o(.text) for USART3_SendData
    data_transfer.o(.text) refers to ffixui.o(.text) for __aeabi_f2uiz
    data_transfer.o(.text) refers to stmflash.o(.text) for STMFLASH_Read
    data_transfer.o(.text) refers to data_transfer.o(.bss) for Blue_dat
    data_transfer.o(.text) refers to ui.o(.data) for BlueCK
    data_transfer.o(.text) refers to data_transfer.o(.data) for Blue_RC
    data_transfer.o(.text) refers to anbt_dmp_mpu6050.o(.data) for Pitch
    data_transfer.o(.text) refers to control.o(.data) for ARMED
    data_transfer.o(.text) refers to control.o(.bss) for PID_Speed
    data_transfer.o(.text) refers to ffltui.o(.text) for __aeabi_ui2f
    data_transfer.o(.text) refers to delay.o(.text) for delay_ms
    ui.o(.text) refers to oled.o(.text) for OLED_Clear
    ui.o(.text) refers to fmul.o(.text) for __aeabi_fmul
    ui.o(.text) refers to ffixi.o(.text) for __aeabi_f2iz
    ui.o(.text) refers to ui.o(.data) for UI_Display_Flag
    ui.o(.text) refers to led.o(.data) for LED_state
    ui.o(.text) refers to control.o(.bss) for PID_Speed
    ui.o(.text) refers to control.o(.data) for PickUp_Flag
    ui.o(.text) refers to data_transfer.o(.data) for time_tick
    ui.o(.text) refers to anbt_dmp_mpu6050.o(.data) for Pitch
    ui.o(.text) refers to data_transfer.o(.text) for Flash_Read_PID
    ui.o(.text) refers to fadd.o(.text) for __aeabi_fadd
    ui.o(.text) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    ui.o(.text) refers to delay.o(.text) for delay_ms
    ui.o(.text) refers to anbt_dmp_mpu6050.o(.text) for mpu_set_dmp_state
    delay.o(.text) refers to delay.o(.data) for fac_us
    sys.o(.text) refers to sys.o(.emb_text) for WFI_SET
    usart.o(.text) refers to ffltui.o(.text) for __aeabi_ui2f
    usart.o(.text) refers to fdiv.o(.text) for __aeabi_fdiv
    usart.o(.text) refers to ffixui.o(.text) for __aeabi_f2uiz
    usart.o(.text) refers to fadd.o(.text) for __aeabi_frsub
    usart.o(.text) refers to fmul.o(.text) for __aeabi_fmul
    led.o(.text) refers to led.o(.data) for Key_C_times
    stmflash.o(.text) refers to delay.o(.text) for delay_us
    stmflash.o(.text) refers to stmflash.o(.bss) for STMFLASH_BUF
    encoder.o(.text) refers to encoder.o(.data) for Encoder_R
    oled.o(.text) refers to delay.o(.text) for delay_ms
    oled.o(.text) refers to oled.o(.bss) for OLED_Buffer
    oled.o(.text) refers to oled.o(.constdata) for ASCII8X16
    dma.o(.text) refers to delay.o(.text) for delay_ms
    ultrasonic.o(.text) refers to sys.o(.text) for MY_NVIC_Init
    ultrasonic.o(.text) refers to ultrasonic.o(.data) for TIM1CH1_CAPTURE_STA
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to ultrasonic.o(.text) for TIM1_CC_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to main.o(.text) for EXTI15_10_IRQHandler
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    anbt_dmp_driver.o(.text) refers to mpu_i2c.o(.text) for MPU_I2C_Start
    anbt_dmp_driver.o(.text) refers to anbt_dmp_mpu6050.o(.text) for mpu_load_firmware
    anbt_dmp_driver.o(.text) refers to anbt_dmp_driver.o(.constdata) for dmp_memory
    anbt_dmp_driver.o(.text) refers to anbt_dmp_driver.o(.bss) for dmp
    anbt_dmp_driver.o(.text) refers to ffltui.o(.text) for __aeabi_ui2f
    anbt_dmp_driver.o(.text) refers to fdiv.o(.text) for __aeabi_fdiv
    anbt_dmp_driver.o(.text) refers to fmul.o(.text) for __aeabi_fmul
    anbt_dmp_driver.o(.text) refers to ffixui.o(.text) for __aeabi_f2uiz
    anbt_dmp_driver.o(.text) refers to memseta.o(.text) for __aeabi_memset
    anbt_dmp_mpu6050.o(.text) refers to delay.o(.text) for delay_ms
    anbt_dmp_mpu6050.o(.text) refers to anbt_dmp_driver.o(.text) for AnBT_DMP_I2C_Write
    anbt_dmp_mpu6050.o(.text) refers to anbt_dmp_mpu6050.o(.data) for st
    anbt_dmp_mpu6050.o(.text) refers to fflti.o(.text) for __aeabi_i2f
    anbt_dmp_mpu6050.o(.text) refers to fdiv.o(.text) for __aeabi_fdiv
    anbt_dmp_mpu6050.o(.text) refers to ffltui.o(.text) for __aeabi_ui2f
    anbt_dmp_mpu6050.o(.text) refers to fmul.o(.text) for __aeabi_fmul
    anbt_dmp_mpu6050.o(.text) refers to fadd.o(.text) for __aeabi_fsub
    anbt_dmp_mpu6050.o(.text) refers to f2d.o(.text) for __aeabi_f2d
    anbt_dmp_mpu6050.o(.text) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    anbt_dmp_mpu6050.o(.text) refers to cfcmple.o(.text) for __aeabi_cfcmple
    anbt_dmp_mpu6050.o(.text) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    anbt_dmp_mpu6050.o(.text) refers to anbt_dmp_mpu6050.o(.constdata) for test
    anbt_dmp_mpu6050.o(.text) refers to ldiv.o(.text) for __aeabi_ldivmod
    anbt_dmp_mpu6050.o(.text) refers to data_transfer.o(.text) for Flash_Save_GyroOffset
    anbt_dmp_mpu6050.o(.text) refers to ffixi.o(.text) for __aeabi_f2iz
    anbt_dmp_mpu6050.o(.text) refers to mpu_i2c.o(.text) for MPU_I2C_Init_IO
    anbt_dmp_mpu6050.o(.text) refers to sys.o(.text) for Ex_NVIC_Config
    anbt_dmp_mpu6050.o(.text) refers to asin.o(i.asin) for asin
    anbt_dmp_mpu6050.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    anbt_dmp_mpu6050.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    anbt_dmp_mpu6050.o(.text) refers to atan2.o(i.atan2) for atan2
    anbt_dmp_mpu6050.o(.text) refers to anbt_dmp_mpu6050.o(.bss) for quat
    anbt_dmp_mpu6050.o(.text) refers to memcmp.o(.text) for memcmp
    anbt_dmp_mpu6050.o(.data) refers to anbt_dmp_mpu6050.o(.constdata) for reg
    mpu_i2c.o(.text) refers to delay.o(.text) for delay_us
    asin.o(i.__softfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.asin) for asin
    asin.o(i.asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.asin) refers to dadd.o(.text) for __aeabi_dadd
    asin.o(i.asin) refers to errno.o(i.__set_errno) for __set_errno
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.asin) refers to dscalb.o(.text) for __ARM_scalbn
    asin.o(i.asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.asin) refers to asin.o(.constdata) for .constdata
    asin.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.__asin$lsc) for __asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.__asin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    asin_x.o(i.__asin$lsc) refers to dadd.o(.text) for __aeabi_dadd
    asin_x.o(i.__asin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    asin_x.o(i.__asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.__asin$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    asin_x.o(i.__asin$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    asin_x.o(i.__asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.__asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    cfcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalb.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing usart.o(.data), (4 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing stmflash.o(.data), (9 bytes).
    Removing encoder.o(.text), (368 bytes).
    Removing encoder.o(.data), (16 bytes).
    Removing adc.o(.text), (364 bytes).
    Removing ultrasonic.o(.bss), (40 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).

8 unused section(s) (total 1345 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/lz77c.c              0x00000000   Number         0  __dclz77c.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  useno.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\DMP\anbt_dmp_driver.c                 0x00000000   Number         0  anbt_dmp_driver.o ABSOLUTE
    ..\DMP\anbt_dmp_mpu6050.c                0x00000000   Number         0  anbt_dmp_mpu6050.o ABSOLUTE
    ..\DMP\mpu_i2c.c                         0x00000000   Number         0  mpu_i2c.o ABSOLUTE
    ..\Hardware\ADC\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\Hardware\OLED\oled.c                  0x00000000   Number         0  oled.o ABSOLUTE
    ..\Hardware\ULTRASONIC\ultrasonic.c      0x00000000   Number         0  ultrasonic.o ABSOLUTE
    ..\Hardware\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Hardware\encoder.c                    0x00000000   Number         0  encoder.o ABSOLUTE
    ..\Hardware\led.c                        0x00000000   Number         0  led.o ABSOLUTE
    ..\Hardware\motor.c                      0x00000000   Number         0  motor.o ABSOLUTE
    ..\Hardware\stmflash.c                   0x00000000   Number         0  stmflash.o ABSOLUTE
    ..\SYSTEM\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\User\Data_Transfer.c                  0x00000000   Number         0  data_transfer.o ABSOLUTE
    ..\User\UI.c                             0x00000000   Number         0  ui.o ABSOLUTE
    ..\User\control.c                        0x00000000   Number         0  control.o ABSOLUTE
    ..\\SYSTEM\\core_cm3.c                   0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\startup\arm\startup_stm32f10x_hd.s    0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    Main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cfcmple.s                                0x00000000   Number         0  cfcmple.o ABSOLUTE
    cfrcmple.s                               0x00000000   Number         0  cfrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000140   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x08000144   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x08000144   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x08000144   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000144   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x08000148   Section       12  sys.o(.emb_text)
    .text                                    0x08000154   Section        0  main.o(.text)
    .text                                    0x080003f0   Section        0  control.o(.text)
    .text                                    0x080006f4   Section        0  data_transfer.o(.text)
    .text                                    0x08000f58   Section        0  ui.o(.text)
    .text                                    0x08002cb8   Section        0  delay.o(.text)
    .text                                    0x08002d68   Section        0  sys.o(.text)
    .text                                    0x08003020   Section        0  usart.o(.text)
    .text                                    0x08003250   Section        0  motor.o(.text)
    .text                                    0x080033e4   Section        0  led.o(.text)
    .text                                    0x08003678   Section        0  stmflash.o(.text)
    .text                                    0x080038a8   Section        0  oled.o(.text)
    .text                                    0x08004750   Section        0  dma.o(.text)
    .text                                    0x080047b8   Section        0  ultrasonic.o(.text)
    .text                                    0x08004a64   Section       28  startup_stm32f10x_hd.o(.text)
    .text                                    0x08004a80   Section        0  anbt_dmp_driver.o(.text)
    decode_gesture                           0x08005615   Thumb Code    94  anbt_dmp_driver.o(.text)
    .text                                    0x080058c8   Section        0  anbt_dmp_mpu6050.o(.text)
    set_int_enable                           0x08005a97   Thumb Code   138  anbt_dmp_mpu6050.o(.text)
    gyro_self_test                           0x08006223   Thumb Code   282  anbt_dmp_mpu6050.o(.text)
    get_accel_prod_shift                     0x0800633d   Thumb Code   168  anbt_dmp_mpu6050.o(.text)
    accel_self_test                          0x080063e5   Thumb Code   186  anbt_dmp_mpu6050.o(.text)
    get_st_biases                            0x0800649f   Thumb Code  1158  anbt_dmp_mpu6050.o(.text)
    inv_row_2_scale                          0x08006bed   Thumb Code    78  anbt_dmp_mpu6050.o(.text)
    inv_orientation_matrix_to_scalar         0x08006c3b   Thumb Code    40  anbt_dmp_mpu6050.o(.text)
    .text                                    0x08007398   Section        0  mpu_i2c.o(.text)
    .text                                    0x08007a34   Section        0  ldiv.o(.text)
    .text                                    0x08007a96   Section        0  memseta.o(.text)
    .text                                    0x08007aba   Section        0  memcmp.o(.text)
    .text                                    0x08007ad4   Section        0  fadd.o(.text)
    .text                                    0x08007b84   Section        0  fmul.o(.text)
    .text                                    0x08007be8   Section        0  fdiv.o(.text)
    .text                                    0x08007c64   Section        0  dmul.o(.text)
    .text                                    0x08007d48   Section        0  fflti.o(.text)
    .text                                    0x08007d5a   Section        0  ffltui.o(.text)
    .text                                    0x08007d64   Section        0  ffixi.o(.text)
    .text                                    0x08007d96   Section        0  ffixui.o(.text)
    .text                                    0x08007dbe   Section        0  f2d.o(.text)
    .text                                    0x08007de4   Section       48  cdcmple.o(.text)
    .text                                    0x08007e14   Section       48  cdrcmple.o(.text)
    .text                                    0x08007e44   Section        0  d2f.o(.text)
    .text                                    0x08007e7c   Section       20  cfcmple.o(.text)
    .text                                    0x08007e90   Section       20  cfrcmple.o(.text)
    .text                                    0x08007ea4   Section        0  uldiv.o(.text)
    .text                                    0x08007f06   Section        0  fepilogue.o(.text)
    .text                                    0x08007f06   Section        0  iusefp.o(.text)
    .text                                    0x08007f74   Section        0  depilogue.o(.text)
    .text                                    0x0800802e   Section        0  dadd.o(.text)
    .text                                    0x0800817c   Section        0  ddiv.o(.text)
    .text                                    0x0800825a   Section        0  dscalb.o(.text)
    .text                                    0x08008288   Section       36  init.o(.text)
    .text                                    0x080082ac   Section        0  llshl.o(.text)
    .text                                    0x080082ca   Section        0  llushr.o(.text)
    .text                                    0x080082ea   Section        0  llsshr.o(.text)
    .text                                    0x0800830e   Section        0  dsqrt.o(.text)
    .text                                    0x080083b0   Section        0  __dclz77c.o(.text)
    i.__ARM_fpclassify                       0x0800840e   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x08008436   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x080084e0   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x080084e6   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x080084ea   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x080084f8   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x08008508   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08008516   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08008518   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08008528   Section        0  errno.o(i.__set_errno)
    i.asin                                   0x08008534   Section        0  asin.o(i.asin)
    i.atan                                   0x080087a4   Section        0  atan.o(i.atan)
    i.atan2                                  0x080089c4   Section        0  atan2.o(i.atan2)
    i.sqrt                                   0x08008b60   Section        0  sqrt.o(i.sqrt)
    .constdata                               0x08008bac   Section     8974  oled.o(.constdata)
    hanzi_12x12                              0x08008bac   Data        3588  oled.o(.constdata)
    Tranhanzi_12x12                          0x080099b0   Data        1976  oled.o(.constdata)
    hanzi_14x14                              0x0800a168   Data         270  oled.o(.constdata)
    ASCII8X16                                0x0800a276   Data        1520  oled.o(.constdata)
    ASCII6X12                                0x0800a866   Data        1140  oled.o(.constdata)
    .constdata                               0x0800aeba   Section     3062  anbt_dmp_driver.o(.constdata)
    dmp_memory                               0x0800aeba   Data        3062  anbt_dmp_driver.o(.constdata)
    .constdata                               0x0800bab0   Section       80  anbt_dmp_mpu6050.o(.constdata)
    .constdata                               0x0800bb00   Section       80  asin.o(.constdata)
    pS                                       0x0800bb00   Data          48  asin.o(.constdata)
    qS                                       0x0800bb30   Data          32  asin.o(.constdata)
    .constdata                               0x0800bb50   Section      152  atan.o(.constdata)
    atanhi                                   0x0800bb50   Data          32  atan.o(.constdata)
    atanlo                                   0x0800bb70   Data          32  atan.o(.constdata)
    aTodd                                    0x0800bb90   Data          40  atan.o(.constdata)
    aTeven                                   0x0800bbb8   Data          48  atan.o(.constdata)
    .constdata                               0x0800bbe8   Section        8  qnan.o(.constdata)
    .data                                    0x20000000   Section       14  main.o(.data)
    gyro_orientation                         0x20000000   Data           9  main.o(.data)
    .data                                    0x20000010   Section       88  control.o(.data)
    gyro_orientation                         0x20000010   Data           9  control.o(.data)
    .data                                    0x20000068   Section       33  data_transfer.o(.data)
    gyro_orientation                         0x20000068   Data           9  data_transfer.o(.data)
    .data                                    0x2000008c   Section     1807  ui.o(.data)
    gyro_orientation                         0x2000008c   Data           9  ui.o(.data)
    .data                                    0x2000079c   Section        4  delay.o(.data)
    fac_us                                   0x2000079c   Data           1  delay.o(.data)
    fac_ms                                   0x2000079e   Data           2  delay.o(.data)
    .data                                    0x200007a0   Section       23  led.o(.data)
    .data                                    0x200007b8   Section       41  ultrasonic.o(.data)
    .data                                    0x200007e4   Section      104  anbt_dmp_mpu6050.o(.data)
    gyro_orientation                         0x200007e4   Data           9  anbt_dmp_mpu6050.o(.data)
    st                                       0x20000820   Data          44  anbt_dmp_mpu6050.o(.data)
    .data                                    0x2000084c   Section        4  errno.o(.data)
    _errno                                   0x2000084c   Data           4  errno.o(.data)
    .bss                                     0x20000850   Section      108  control.o(.bss)
    .bss                                     0x200008bc   Section       64  data_transfer.o(.bss)
    .bss                                     0x200008fc   Section     1024  stmflash.o(.bss)
    .bss                                     0x20000cfc   Section     1024  oled.o(.bss)
    .bss                                     0x200010fc   Section       16  anbt_dmp_driver.o(.bss)
    dmp                                      0x200010fc   Data          16  anbt_dmp_driver.o(.bss)
    .bss                                     0x2000110c   Section       16  anbt_dmp_mpu6050.o(.bss)
    STACK                                    0x20001120   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __use_no_errno                           0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_exception_handling              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_fp                              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap                            0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap_region                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting_swi                 0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_signal_handling                 0x00000000   Number         0  useno.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000141   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x08000145   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x08000145   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    WFI_SET                                  0x08000149   Thumb Code     2  sys.o(.emb_text)
    INTX_DISABLE                             0x0800014b   Thumb Code     2  sys.o(.emb_text)
    INTX_ENABLE                              0x0800014d   Thumb Code     2  sys.o(.emb_text)
    MSR_MSP                                  0x0800014f   Thumb Code     6  sys.o(.emb_text)
    main                                     0x08000155   Thumb Code   264  main.o(.text)
    EXTI15_10_IRQHandler                     0x0800025d   Thumb Code   296  main.o(.text)
    PID_Init                                 0x080003f1   Thumb Code   112  control.o(.text)
    CONTROL                                  0x08000461   Thumb Code   544  control.o(.text)
    BlueData_Receive_Anl                     0x080006f5   Thumb Code   182  data_transfer.o(.text)
    Data_Send_Attitude                       0x080007ab   Thumb Code   238  data_transfer.o(.text)
    Data_Send_PID1                           0x08000899   Thumb Code   374  data_transfer.o(.text)
    Send_Data                                0x08000a0f   Thumb Code    44  data_transfer.o(.text)
    Flash_Save_GyroOffset                    0x08000a3b   Thumb Code   104  data_transfer.o(.text)
    Flash_Read_GyroOffset                    0x08000aa3   Thumb Code   158  data_transfer.o(.text)
    Flash_Save_AccOffset                     0x08000b41   Thumb Code   122  data_transfer.o(.text)
    Flash_Read_AccOffset                     0x08000bbb   Thumb Code   100  data_transfer.o(.text)
    Flash_Save_PID                           0x08000c1f   Thumb Code   216  data_transfer.o(.text)
    Flash_Read_PID                           0x08000cf7   Thumb Code   188  data_transfer.o(.text)
    Flash_Save_SafeAngle                     0x08000db3   Thumb Code    66  data_transfer.o(.text)
    Flash_Read_SafeAngle                     0x08000df5   Thumb Code    54  data_transfer.o(.text)
    Flash_Save_Language                      0x08000e2b   Thumb Code    50  data_transfer.o(.text)
    Flash_Read_Language                      0x08000e5d   Thumb Code    64  data_transfer.o(.text)
    Flash_Save_VoltWarn                      0x08000e9d   Thumb Code    50  data_transfer.o(.text)
    Flash_Read_VoltWarn                      0x08000ecf   Thumb Code    64  data_transfer.o(.text)
    Flash_Save_Delay                         0x08000f0f   Thumb Code    74  data_transfer.o(.text)
    Standby_Mode                             0x08000f59   Thumb Code   560  ui.o(.text)
    Balance_Mode                             0x08001189   Thumb Code   578  ui.o(.text)
    Display_Menux                            0x080013cb   Thumb Code   246  ui.o(.text)
    Settings_Mode                            0x080014c1   Thumb Code   838  ui.o(.text)
    Display_SaveMasBox                       0x08001807   Thumb Code    86  ui.o(.text)
    Display_PID_data                         0x0800185d   Thumb Code   438  ui.o(.text)
    PID_Settings                             0x08001a13   Thumb Code  1258  ui.o(.text)
    Display_SafeAngle_data                   0x08001efd   Thumb Code   160  ui.o(.text)
    SafeAngle_Settings                       0x08001f9d   Thumb Code   604  ui.o(.text)
    AdjustAngle_Settings                     0x080021f9   Thumb Code   538  ui.o(.text)
    Display_LanguageCursor                   0x08002413   Thumb Code   194  ui.o(.text)
    LanguageSettings_Mode                    0x080024d5   Thumb Code   406  ui.o(.text)
    Display_FactoryBox                       0x0800266b   Thumb Code    90  ui.o(.text)
    FactorySettings_Mode                     0x080026c5   Thumb Code   488  ui.o(.text)
    Display_OtherSettings_data               0x080028ad   Thumb Code   306  ui.o(.text)
    Other_Settings_Mode                      0x080029df   Thumb Code   528  ui.o(.text)
    VoltWarn_Mode                            0x08002bef   Thumb Code   118  ui.o(.text)
    Delay_Init                               0x08002cb9   Thumb Code    56  delay.o(.text)
    delay_ms                                 0x08002cf1   Thumb Code    56  delay.o(.text)
    delay_us                                 0x08002d29   Thumb Code    56  delay.o(.text)
    MY_NVIC_SetVectorTable                   0x08002d69   Thumb Code    12  sys.o(.text)
    MY_NVIC_PriorityGroupConfig              0x08002d75   Thumb Code    36  sys.o(.text)
    MY_NVIC_Init                             0x08002d99   Thumb Code   106  sys.o(.text)
    Ex_NVIC_Config                           0x08002e03   Thumb Code   146  sys.o(.text)
    MYRCC_DeInit                             0x08002e95   Thumb Code    90  sys.o(.text)
    Sys_Standby                              0x08002eef   Thumb Code    68  sys.o(.text)
    Sys_Soft_Reset                           0x08002f33   Thumb Code    12  sys.o(.text)
    JTAG_Set                                 0x08002f3f   Thumb Code    42  sys.o(.text)
    Stm32_Clock_Init                         0x08002f69   Thumb Code   134  sys.o(.text)
    _sys_exit                                0x08003021   Thumb Code     6  usart.o(.text)
    fputc                                    0x08003027   Thumb Code    24  usart.o(.text)
    USART1_SendData                          0x0800303f   Thumb Code    22  usart.o(.text)
    USART1_SendString                        0x08003055   Thumb Code    22  usart.o(.text)
    USART1_Init                              0x0800306b   Thumb Code   198  usart.o(.text)
    USART3_SendData                          0x08003131   Thumb Code    22  usart.o(.text)
    USART3_SendString                        0x08003147   Thumb Code    22  usart.o(.text)
    USART3_Init                              0x0800315d   Thumb Code   206  usart.o(.text)
    TIM1_PWM_Init                            0x08003251   Thumb Code   236  motor.o(.text)
    Set_Motor                                0x0800333d   Thumb Code    70  motor.o(.text)
    Motor_Init                               0x08003383   Thumb Code    72  motor.o(.text)
    LED_Init                                 0x080033e5   Thumb Code   102  led.o(.text)
    Key_Check                                0x0800344b   Thumb Code   380  led.o(.text)
    Led_Flash3                               0x080035c7   Thumb Code     2  led.o(.text)
    Led_Flash2                               0x080035c9   Thumb Code    28  led.o(.text)
    Led_Flash1                               0x080035e5   Thumb Code    10  led.o(.text)
    LED_Ring_Control                         0x080035ef   Thumb Code    66  led.o(.text)
    STMFLASH_Unlock                          0x08003679   Thumb Code    12  stmflash.o(.text)
    STMFLASH_Lock                            0x08003685   Thumb Code    14  stmflash.o(.text)
    STMFLASH_GetStatus                       0x08003693   Thumb Code    38  stmflash.o(.text)
    STMFLASH_WaitDone                        0x080036b9   Thumb Code    42  stmflash.o(.text)
    STMFLASH_ErasePage                       0x080036e3   Thumb Code    72  stmflash.o(.text)
    STMFLASH_WriteHalfWord                   0x0800372b   Thumb Code    58  stmflash.o(.text)
    STMFLASH_ReadHalfWord                    0x08003765   Thumb Code     6  stmflash.o(.text)
    STMFLASH_Write_NoCheck                   0x0800376b   Thumb Code    38  stmflash.o(.text)
    STMFLASH_Read                            0x08003791   Thumb Code    34  stmflash.o(.text)
    STMFLASH_Write                           0x080037b3   Thumb Code   224  stmflash.o(.text)
    OLED_I2C_Stop                            0x080038a9   Thumb Code    48  oled.o(.text)
    OLED_I2C_Wait_Ack                        0x080038d9   Thumb Code    76  oled.o(.text)
    OLED_I2C_Send_Byte                       0x08003925   Thumb Code    76  oled.o(.text)
    OLED_I2C_Start                           0x08003971   Thumb Code    52  oled.o(.text)
    OLED_WriteCmd                            0x080039a5   Thumb Code    32  oled.o(.text)
    OLED_Set_Pos                             0x080039c5   Thumb Code    36  oled.o(.text)
    OLED_WriteDat                            0x080039e9   Thumb Code    32  oled.o(.text)
    OLED_Fill                                0x08003a09   Thumb Code    58  oled.o(.text)
    OLED_Init                                0x08003a43   Thumb Code   246  oled.o(.text)
    OLED_Refresh_GDRAM                       0x08003b39   Thumb Code    38  oled.o(.text)
    OLED_Refresh_AllGDRAM                    0x08003b5f   Thumb Code    52  oled.o(.text)
    OLED_Draw_dots                           0x08003b93   Thumb Code    88  oled.o(.text)
    OLED_PutChar                             0x08003beb   Thumb Code   206  oled.o(.text)
    OLED_PutChar0612                         0x08003cb9   Thumb Code   154  oled.o(.text)
    OLED_PutChar5x7                          0x08003d53   Thumb Code   136  oled.o(.text)
    OLED_WriteChstr1414                      0x08003ddb   Thumb Code   330  oled.o(.text)
    OLED_WriteChstr1212                      0x08003f25   Thumb Code   346  oled.o(.text)
    OLED_WriteENstr5x7                       0x0800407f   Thumb Code    38  oled.o(.text)
    OLED_Drawline_X                          0x080040a5   Thumb Code    64  oled.o(.text)
    OLED_Drawline_Y                          0x080040e5   Thumb Code    46  oled.o(.text)
    OLED_Drawline                            0x08004113   Thumb Code   264  oled.o(.text)
    OLED_Draw_lingxing                       0x0800421b   Thumb Code   230  oled.o(.text)
    OLED_Draw_circle                         0x08004301   Thumb Code   230  oled.o(.text)
    OLED_Clear                               0x080043e7   Thumb Code    36  oled.o(.text)
    OLED_WriteStr                            0x0800440b   Thumb Code   216  oled.o(.text)
    OLED_Display0612Num                      0x080044e3   Thumb Code   204  oled.o(.text)
    OLED_Display0612Num1dot                  0x080045af   Thumb Code   208  oled.o(.text)
    OLED_Display0507Num1dot                  0x0800467f   Thumb Code   202  oled.o(.text)
    MYDMA_Config                             0x08004751   Thumb Code   100  dma.o(.text)
    TIM3_Init                                0x080047b9   Thumb Code   210  ultrasonic.o(.text)
    ultrasonic_init                          0x0800488b   Thumb Code    62  ultrasonic.o(.text)
    TIM1_CC_IRQHandler                       0x080048c9   Thumb Code   362  ultrasonic.o(.text)
    Reset_Handler                            0x08004a65   Thumb Code     4  startup_stm32f10x_hd.o(.text)
    NMI_Handler                              0x08004a69   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    HardFault_Handler                        0x08004a6b   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    MemManage_Handler                        0x08004a6d   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    BusFault_Handler                         0x08004a6f   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    UsageFault_Handler                       0x08004a71   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SVC_Handler                              0x08004a73   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    DebugMon_Handler                         0x08004a75   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    PendSV_Handler                           0x08004a77   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SysTick_Handler                          0x08004a79   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08004a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    AnBT_DMP_I2C_Write                       0x08004a81   Thumb Code    94  anbt_dmp_driver.o(.text)
    AnBT_DMP_I2C_Read                        0x08004adf   Thumb Code   128  anbt_dmp_driver.o(.text)
    dmp_load_motion_driver_firmware          0x08004b5f   Thumb Code    20  anbt_dmp_driver.o(.text)
    dmp_set_orientation                      0x08004b73   Thumb Code   290  anbt_dmp_driver.o(.text)
    dmp_set_gyro_bias                        0x08004c95   Thumb Code   294  anbt_dmp_driver.o(.text)
    dmp_set_accel_bias                       0x08004dbb   Thumb Code   300  anbt_dmp_driver.o(.text)
    dmp_set_fifo_rate                        0x08004ee7   Thumb Code   138  anbt_dmp_driver.o(.text)
    dmp_get_fifo_rate                        0x08004f71   Thumb Code    12  anbt_dmp_driver.o(.text)
    dmp_set_tap_thresh                       0x08004f7d   Thumb Code   396  anbt_dmp_driver.o(.text)
    dmp_set_tap_axes                         0x08005109   Thumb Code    70  anbt_dmp_driver.o(.text)
    dmp_set_tap_count                        0x0800514f   Thumb Code    38  anbt_dmp_driver.o(.text)
    dmp_set_tap_time                         0x08005175   Thumb Code    38  anbt_dmp_driver.o(.text)
    dmp_set_tap_time_multi                   0x0800519b   Thumb Code    38  anbt_dmp_driver.o(.text)
    dmp_set_shake_reject_thresh              0x080051c1   Thumb Code    56  anbt_dmp_driver.o(.text)
    dmp_set_shake_reject_time                0x080051f9   Thumb Code    38  anbt_dmp_driver.o(.text)
    dmp_set_shake_reject_timeout             0x0800521f   Thumb Code    38  anbt_dmp_driver.o(.text)
    dmp_get_pedometer_step_count             0x08005245   Thumb Code    66  anbt_dmp_driver.o(.text)
    dmp_set_pedometer_step_count             0x08005287   Thumb Code    42  anbt_dmp_driver.o(.text)
    dmp_get_pedometer_walk_time              0x080052b1   Thumb Code    72  anbt_dmp_driver.o(.text)
    dmp_set_pedometer_walk_time              0x080052f9   Thumb Code    48  anbt_dmp_driver.o(.text)
    dmp_enable_6x_lp_quat                    0x08005329   Thumb Code    84  anbt_dmp_driver.o(.text)
    dmp_enable_lp_quat                       0x0800537d   Thumb Code    60  anbt_dmp_driver.o(.text)
    dmp_enable_gyro_cal                      0x080053b9   Thumb Code    62  anbt_dmp_driver.o(.text)
    dmp_enable_feature                       0x080053f7   Thumb Code   530  anbt_dmp_driver.o(.text)
    dmp_get_enabled_features                 0x08005609   Thumb Code    12  anbt_dmp_driver.o(.text)
    dmp_set_interrupt_mode                   0x08005673   Thumb Code    66  anbt_dmp_driver.o(.text)
    dmp_read_fifo                            0x080056b5   Thumb Code   508  anbt_dmp_driver.o(.text)
    dmp_register_tap_cb                      0x080058b1   Thumb Code    10  anbt_dmp_driver.o(.text)
    dmp_register_android_orient_cb           0x080058bb   Thumb Code    10  anbt_dmp_driver.o(.text)
    AnBT_DMP_Delay_ms                        0x080058c9   Thumb Code    12  anbt_dmp_mpu6050.o(.text)
    mpu_reset_fifo                           0x080058d5   Thumb Code   450  anbt_dmp_mpu6050.o(.text)
    mpu_set_lpf                              0x08005b21   Thumb Code   126  anbt_dmp_mpu6050.o(.text)
    mpu_configure_fifo                       0x08005b9f   Thumb Code   106  anbt_dmp_mpu6050.o(.text)
    mpu_set_int_latched                      0x08005c09   Thumb Code   102  anbt_dmp_mpu6050.o(.text)
    mpu_lp_accel_mode                        0x08005c6f   Thumb Code   224  anbt_dmp_mpu6050.o(.text)
    mpu_set_sample_rate                      0x08005d4f   Thumb Code   152  anbt_dmp_mpu6050.o(.text)
    mpu_set_bypass                           0x08005de7   Thumb Code   328  anbt_dmp_mpu6050.o(.text)
    mpu_set_dmp_state                        0x08005f2f   Thumb Code   138  anbt_dmp_mpu6050.o(.text)
    mpu_get_accel_sens                       0x08005fb9   Thumb Code    78  anbt_dmp_mpu6050.o(.text)
    mpu_get_gyro_sens                        0x08006007   Thumb Code    58  anbt_dmp_mpu6050.o(.text)
    mpu_set_sensors                          0x08006041   Thumb Code   224  anbt_dmp_mpu6050.o(.text)
    mpu_set_accel_fsr                        0x08006121   Thumb Code   126  anbt_dmp_mpu6050.o(.text)
    mpu_set_gyro_fsr                         0x0800619f   Thumb Code   132  anbt_dmp_mpu6050.o(.text)
    mpu_get_fifo_config                      0x08006925   Thumb Code    12  anbt_dmp_mpu6050.o(.text)
    mpu_get_sample_rate                      0x08006931   Thumb Code    26  anbt_dmp_mpu6050.o(.text)
    mpu_get_lpf                              0x0800694b   Thumb Code    74  anbt_dmp_mpu6050.o(.text)
    mpu_get_accel_fsr                        0x08006995   Thumb Code    72  anbt_dmp_mpu6050.o(.text)
    mpu_get_gyro_fsr                         0x080069dd   Thumb Code    64  anbt_dmp_mpu6050.o(.text)
    mpu_run_self_test                        0x08006a1d   Thumb Code   278  anbt_dmp_mpu6050.o(.text)
    run_self_test                            0x08006b33   Thumb Code   186  anbt_dmp_mpu6050.o(.text)
    AnBT_DMP_MPU6050_DEV_CFG                 0x08006c63   Thumb Code   414  anbt_dmp_mpu6050.o(.text)
    AnBT_DMP_MPU6050_Init                    0x08006e01   Thumb Code   140  anbt_dmp_mpu6050.o(.text)
    MPU6050_Pose                             0x08006e8d   Thumb Code   572  anbt_dmp_mpu6050.o(.text)
    mpu_write_mem                            0x080070c9   Thumb Code   196  anbt_dmp_mpu6050.o(.text)
    mpu_read_mem                             0x0800718d   Thumb Code   122  anbt_dmp_mpu6050.o(.text)
    mpu_read_fifo_stream                     0x08007207   Thumb Code   186  anbt_dmp_mpu6050.o(.text)
    mpu_load_firmware                        0x080072c1   Thumb Code   180  anbt_dmp_mpu6050.o(.text)
    number_to_dps                            0x08007375   Thumb Code    26  anbt_dmp_mpu6050.o(.text)
    MPU_I2C_Init_IO                          0x08007399   Thumb Code   100  mpu_i2c.o(.text)
    MPU_I2C_Start                            0x080073fd   Thumb Code    60  mpu_i2c.o(.text)
    MPU_I2C_Stop                             0x08007439   Thumb Code    60  mpu_i2c.o(.text)
    MPU_I2C_WaitAck                          0x08007475   Thumb Code    88  mpu_i2c.o(.text)
    MPU_I2C_Ack                              0x080074cd   Thumb Code    62  mpu_i2c.o(.text)
    MPU_I2C_NoAck                            0x0800750b   Thumb Code    60  mpu_i2c.o(.text)
    MPU_I2C_SendByte                         0x08007547   Thumb Code    78  mpu_i2c.o(.text)
    MPU_I2C_ReadByte                         0x08007595   Thumb Code   114  mpu_i2c.o(.text)
    MPU_I2C_Start2                           0x08007607   Thumb Code    70  mpu_i2c.o(.text)
    MPU_I2C_Stop2                            0x0800764d   Thumb Code    78  mpu_i2c.o(.text)
    MPU_I2C_WaitAck2                         0x0800769b   Thumb Code   106  mpu_i2c.o(.text)
    MPU_I2C_Ack2                             0x08007705   Thumb Code    78  mpu_i2c.o(.text)
    MPU_I2C_NoAck2                           0x08007753   Thumb Code   108  mpu_i2c.o(.text)
    MPU_I2C_SendByte2                        0x080077bf   Thumb Code    98  mpu_i2c.o(.text)
    MPU_I2C_ReadByte2                        0x08007821   Thumb Code    98  mpu_i2c.o(.text)
    Single_Write                             0x08007883   Thumb Code    74  mpu_i2c.o(.text)
    Single_Write2                            0x080078cd   Thumb Code    62  mpu_i2c.o(.text)
    Single_Read                              0x0800790b   Thumb Code    84  mpu_i2c.o(.text)
    Multiple_write                           0x0800795f   Thumb Code    84  mpu_i2c.o(.text)
    Multiple_read                            0x080079b3   Thumb Code   112  mpu_i2c.o(.text)
    __aeabi_ldivmod                          0x08007a35   Thumb Code    98  ldiv.o(.text)
    __aeabi_memset                           0x08007a97   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08007a97   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08007a97   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08007aa5   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08007aa5   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08007aa5   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08007aa9   Thumb Code    18  memseta.o(.text)
    memcmp                                   0x08007abb   Thumb Code    26  memcmp.o(.text)
    __aeabi_fadd                             0x08007ad5   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x08007b79   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x08007b7f   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x08007b85   Thumb Code   100  fmul.o(.text)
    __aeabi_fdiv                             0x08007be9   Thumb Code   124  fdiv.o(.text)
    __aeabi_dmul                             0x08007c65   Thumb Code   228  dmul.o(.text)
    __aeabi_i2f                              0x08007d49   Thumb Code    18  fflti.o(.text)
    __aeabi_ui2f                             0x08007d5b   Thumb Code    10  ffltui.o(.text)
    __aeabi_f2iz                             0x08007d65   Thumb Code    50  ffixi.o(.text)
    __aeabi_f2uiz                            0x08007d97   Thumb Code    40  ffixui.o(.text)
    __aeabi_f2d                              0x08007dbf   Thumb Code    38  f2d.o(.text)
    __aeabi_cdcmpeq                          0x08007de5   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x08007de5   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x08007e15   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_d2f                              0x08007e45   Thumb Code    56  d2f.o(.text)
    __aeabi_cfcmpeq                          0x08007e7d   Thumb Code     0  cfcmple.o(.text)
    __aeabi_cfcmple                          0x08007e7d   Thumb Code    20  cfcmple.o(.text)
    __aeabi_cfrcmple                         0x08007e91   Thumb Code    20  cfrcmple.o(.text)
    __aeabi_uldivmod                         0x08007ea5   Thumb Code    98  uldiv.o(.text)
    __I$use$fp                               0x08007f07   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08007f07   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08007f19   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08007f75   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08007f93   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x0800802f   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08008171   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08008177   Thumb Code     6  dadd.o(.text)
    __aeabi_ddiv                             0x0800817d   Thumb Code   222  ddiv.o(.text)
    __ARM_scalbn                             0x0800825b   Thumb Code    46  dscalb.o(.text)
    scalbn                                   0x0800825b   Thumb Code     0  dscalb.o(.text)
    __scatterload                            0x08008289   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08008289   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x080082ad   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080082ad   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080082cb   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080082cb   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x080082eb   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080082eb   Thumb Code     0  llsshr.o(.text)
    _dsqrt                                   0x0800830f   Thumb Code   162  dsqrt.o(.text)
    __decompress                             0x080083b1   Thumb Code     0  __dclz77c.o(.text)
    __decompress2                            0x080083b1   Thumb Code    94  __dclz77c.o(.text)
    __ARM_fpclassify                         0x0800840f   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x08008437   Thumb Code   170  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x080084e1   Thumb Code     6  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x080084e7   Thumb Code     4  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x080084eb   Thumb Code    12  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x080084f9   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x08008509   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08008517   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08008519   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08008529   Thumb Code     6  errno.o(i.__set_errno)
    asin                                     0x08008535   Thumb Code   572  asin.o(i.asin)
    atan                                     0x080087a5   Thumb Code   474  atan.o(i.atan)
    atan2                                    0x080089c5   Thumb Code   374  atan2.o(i.atan2)
    sqrt                                     0x08008b61   Thumb Code    76  sqrt.o(i.sqrt)
    ASCII5x7                                 0x0800acda   Data         480  oled.o(.constdata)
    reg                                      0x0800bab0   Data          27  anbt_dmp_mpu6050.o(.constdata)
    hw                                       0x0800bacc   Data          12  anbt_dmp_mpu6050.o(.constdata)
    test                                     0x0800bad8   Data          40  anbt_dmp_mpu6050.o(.constdata)
    __mathlib_zero                           0x0800bbe8   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x0800bbf0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800bc10   Number         0  anon$$obj.o(Region$$Table)
    PS2_Flag                                 0x20000009   Data           1  main.o(.data)
    displaystick                             0x2000000a   Data           1  main.o(.data)
    RC_Last                                  0x2000000c   Data           2  main.o(.data)
    MotorL_PWM                               0x2000001c   Data           4  control.o(.data)
    MotorR_PWM                               0x20000020   Data           4  control.o(.data)
    ARMED                                    0x20000024   Data           1  control.o(.data)
    Front_SafeAngle                          0x20000026   Data           2  control.o(.data)
    Back_SafeAngle                           0x20000028   Data           2  control.o(.data)
    yaw_gyro_last                            0x2000002c   Data           4  control.o(.data)
    pit_gyro_now                             0x20000030   Data           4  control.o(.data)
    yaw_gyro_now                             0x20000034   Data           4  control.o(.data)
    yaw_gyro_last0                           0x20000038   Data           4  control.o(.data)
    yaw_gyro_last1                           0x2000003c   Data           4  control.o(.data)
    SpeedControl_stik                        0x20000040   Data           1  control.o(.data)
    PickUp_Flag                              0x20000041   Data           1  control.o(.data)
    Speed_tar_Last                           0x20000044   Data           4  control.o(.data)
    Speed_ILimtFlag                          0x20000048   Data           1  control.o(.data)
    FullSpeed_CK                             0x20000049   Data           1  control.o(.data)
    Speed_Xishu                              0x2000004c   Data           4  control.o(.data)
    Banlan_Xishu                             0x20000050   Data           4  control.o(.data)
    Encode0                                  0x20000054   Data           4  control.o(.data)
    Encode1                                  0x20000058   Data           4  control.o(.data)
    Encode2                                  0x2000005c   Data           4  control.o(.data)
    PID_Speed_OUT0                           0x20000060   Data           4  control.o(.data)
    PID_Speed_OUT1                           0x20000064   Data           4  control.o(.data)
    Blue_RC                                  0x20000072   Data           6  data_transfer.o(.data)
    LeftKnob                                 0x20000078   Data           4  data_transfer.o(.data)
    RightKnob                                0x2000007c   Data           4  data_transfer.o(.data)
    Send_Attitude                            0x20000080   Data           1  data_transfer.o(.data)
    Send_Senser                              0x20000081   Data           1  data_transfer.o(.data)
    Send_RCData                              0x20000082   Data           1  data_transfer.o(.data)
    Send_PID1                                0x20000083   Data           1  data_transfer.o(.data)
    time_tick                                0x20000084   Data           4  data_transfer.o(.data)
    Control_Flag                             0x20000088   Data           1  data_transfer.o(.data)
    WorkMode                                 0x20000095   Data           1  ui.o(.data)
    Volt                                     0x20000098   Data           4  ui.o(.data)
    Menux                                    0x2000009c   Data           1  ui.o(.data)
    AdjustAngle_Flag                         0x2000009e   Data           2  ui.o(.data)
    BlueCK                                   0x200000a0   Data           1  ui.o(.data)
    Lang_x                                   0x200000a2   Data           2  ui.o(.data)
    UI_Display_Flag                          0x200000a4   Data           1  ui.o(.data)
    VoltLowWarn                              0x200000a6   Data           2  ui.o(.data)
    VoltLowTimes                             0x200000a8   Data           2  ui.o(.data)
    Save_PID_Flag                            0x200000aa   Data           1  ui.o(.data)
    Title_Strings                            0x200000ab   Data         468  ui.o(.data)
    StandbyUI_String                         0x2000027f   Data         168  ui.o(.data)
    StandbyUI_String2                        0x20000327   Data         126  ui.o(.data)
    SettingUI_String                         0x200003a5   Data         264  ui.o(.data)
    PIDsetUI_String                          0x200004ad   Data          75  ui.o(.data)
    SafeAngleUI_String                       0x200004f8   Data         135  ui.o(.data)
    MasBox_String                            0x2000057f   Data         180  ui.o(.data)
    CALI_String                              0x20000633   Data         264  ui.o(.data)
    CALI_String2                             0x2000073b   Data          36  ui.o(.data)
    Factory_String                           0x2000075f   Data          60  ui.o(.data)
    LED_state                                0x200007a0   Data           1  led.o(.data)
    LED_timetick                             0x200007a2   Data           2  led.o(.data)
    Key_C_times                              0x200007a4   Data           2  led.o(.data)
    Key_C_sta                                0x200007a6   Data           1  led.o(.data)
    Key_U_times                              0x200007a8   Data           2  led.o(.data)
    Key_U_sta                                0x200007aa   Data           1  led.o(.data)
    Key_D_times                              0x200007ac   Data           2  led.o(.data)
    Key_D_sta                                0x200007ae   Data           1  led.o(.data)
    Key_L_times                              0x200007b0   Data           2  led.o(.data)
    Key_L_sta                                0x200007b2   Data           1  led.o(.data)
    Key_R_times                              0x200007b4   Data           2  led.o(.data)
    Key_R_sta                                0x200007b6   Data           1  led.o(.data)
    TIM1CH1_CAPTURE_STA                      0x200007b8   Data           1  ultrasonic.o(.data)
    TIM1CH1_CAPTURE_UPVAL                    0x200007bc   Data           4  ultrasonic.o(.data)
    TIM1CH1_CAPTURE_DOWNVAL                  0x200007c0   Data           4  ultrasonic.o(.data)
    ultr_High                                0x200007c4   Data           4  ultrasonic.o(.data)
    ultr_High_rec                            0x200007c8   Data          20  ultrasonic.o(.data)
    ultr_temp                                0x200007dc   Data           4  ultrasonic.o(.data)
    ultr_OK                                  0x200007e0   Data           1  ultrasonic.o(.data)
    q0                                       0x200007f0   Data           4  anbt_dmp_mpu6050.o(.data)
    q1                                       0x200007f4   Data           4  anbt_dmp_mpu6050.o(.data)
    q2                                       0x200007f8   Data           4  anbt_dmp_mpu6050.o(.data)
    q3                                       0x200007fc   Data           4  anbt_dmp_mpu6050.o(.data)
    sensor_timestamp                         0x20000800   Data           4  anbt_dmp_mpu6050.o(.data)
    MPU6050_gyro                             0x20000804   Data           6  anbt_dmp_mpu6050.o(.data)
    MPU6050_accel                            0x2000080a   Data           6  anbt_dmp_mpu6050.o(.data)
    sensors                                  0x20000810   Data           2  anbt_dmp_mpu6050.o(.data)
    more                                     0x20000812   Data           1  anbt_dmp_mpu6050.o(.data)
    Yaw                                      0x20000814   Data           4  anbt_dmp_mpu6050.o(.data)
    Roll                                     0x20000818   Data           4  anbt_dmp_mpu6050.o(.data)
    Pitch                                    0x2000081c   Data           4  anbt_dmp_mpu6050.o(.data)
    PID_Speed                                0x20000850   Data          36  control.o(.bss)
    PID_PIT                                  0x20000874   Data          36  control.o(.bss)
    PID_YAW                                  0x20000898   Data          36  control.o(.bss)
    data_to_send                             0x200008bc   Data          32  data_transfer.o(.bss)
    Blue_dat                                 0x200008dc   Data          32  data_transfer.o(.bss)
    STMFLASH_BUF                             0x200008fc   Data        1024  stmflash.o(.bss)
    OLED_Buffer                              0x20000cfc   Data        1024  oled.o(.bss)
    quat                                     0x2000110c   Data          16  anbt_dmp_mpu6050.o(.bss)
    __initial_sp                             0x20001520   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000c460, Max: 0x00010000, ABSOLUTE, COMPRESSED[0x0000c058])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000bc10, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          375    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO          451  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO          525    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO          528    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO          530    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO          532    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO          533    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000004   Code   RO          540    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x08000144   0x08000144   0x00000000   Code   RO          535    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x08000144   0x08000144   0x00000000   Code   RO          537    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x08000144   0x08000144   0x00000004   Code   RO          526    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000148   0x08000148   0x0000000c   Code   RO          192    .emb_text           sys.o
    0x08000154   0x08000154   0x0000029c   Code   RO            1    .text               main.o
    0x080003f0   0x080003f0   0x00000304   Code   RO          108    .text               control.o
    0x080006f4   0x080006f4   0x00000864   Code   RO          138    .text               data_transfer.o
    0x08000f58   0x08000f58   0x00001d60   Code   RO          160    .text               ui.o
    0x08002cb8   0x08002cb8   0x000000b0   Code   RO          178    .text               delay.o
    0x08002d68   0x08002d68   0x000002b8   Code   RO          193    .text               sys.o
    0x08003020   0x08003020   0x00000230   Code   RO          209    .text               usart.o
    0x08003250   0x08003250   0x00000194   Code   RO          238    .text               motor.o
    0x080033e4   0x080033e4   0x00000294   Code   RO          250    .text               led.o
    0x08003678   0x08003678   0x00000230   Code   RO          268    .text               stmflash.o
    0x080038a8   0x080038a8   0x00000ea8   Code   RO          305    .text               oled.o
    0x08004750   0x08004750   0x00000068   Code   RO          342    .text               dma.o
    0x080047b8   0x080047b8   0x000002ac   Code   RO          354    .text               ultrasonic.o
    0x08004a64   0x08004a64   0x0000001c   Code   RO          376    .text               startup_stm32f10x_hd.o
    0x08004a80   0x08004a80   0x00000e48   Code   RO          380    .text               anbt_dmp_driver.o
    0x080058c8   0x080058c8   0x00001ad0   Code   RO          398    .text               anbt_dmp_mpu6050.o
    0x08007398   0x08007398   0x0000069c   Code   RO          418    .text               mpu_i2c.o
    0x08007a34   0x08007a34   0x00000062   Code   RO          454    .text               mc_w.l(ldiv.o)
    0x08007a96   0x08007a96   0x00000024   Code   RO          456    .text               mc_w.l(memseta.o)
    0x08007aba   0x08007aba   0x0000001a   Code   RO          458    .text               mc_w.l(memcmp.o)
    0x08007ad4   0x08007ad4   0x000000b0   Code   RO          460    .text               mf_w.l(fadd.o)
    0x08007b84   0x08007b84   0x00000064   Code   RO          462    .text               mf_w.l(fmul.o)
    0x08007be8   0x08007be8   0x0000007c   Code   RO          464    .text               mf_w.l(fdiv.o)
    0x08007c64   0x08007c64   0x000000e4   Code   RO          466    .text               mf_w.l(dmul.o)
    0x08007d48   0x08007d48   0x00000012   Code   RO          468    .text               mf_w.l(fflti.o)
    0x08007d5a   0x08007d5a   0x0000000a   Code   RO          470    .text               mf_w.l(ffltui.o)
    0x08007d64   0x08007d64   0x00000032   Code   RO          472    .text               mf_w.l(ffixi.o)
    0x08007d96   0x08007d96   0x00000028   Code   RO          474    .text               mf_w.l(ffixui.o)
    0x08007dbe   0x08007dbe   0x00000026   Code   RO          476    .text               mf_w.l(f2d.o)
    0x08007de4   0x08007de4   0x00000030   Code   RO          478    .text               mf_w.l(cdcmple.o)
    0x08007e14   0x08007e14   0x00000030   Code   RO          480    .text               mf_w.l(cdrcmple.o)
    0x08007e44   0x08007e44   0x00000038   Code   RO          482    .text               mf_w.l(d2f.o)
    0x08007e7c   0x08007e7c   0x00000014   Code   RO          484    .text               mf_w.l(cfcmple.o)
    0x08007e90   0x08007e90   0x00000014   Code   RO          486    .text               mf_w.l(cfrcmple.o)
    0x08007ea4   0x08007ea4   0x00000062   Code   RO          541    .text               mc_w.l(uldiv.o)
    0x08007f06   0x08007f06   0x00000000   Code   RO          550    .text               mc_w.l(iusefp.o)
    0x08007f06   0x08007f06   0x0000006e   Code   RO          551    .text               mf_w.l(fepilogue.o)
    0x08007f74   0x08007f74   0x000000ba   Code   RO          553    .text               mf_w.l(depilogue.o)
    0x0800802e   0x0800802e   0x0000014e   Code   RO          555    .text               mf_w.l(dadd.o)
    0x0800817c   0x0800817c   0x000000de   Code   RO          557    .text               mf_w.l(ddiv.o)
    0x0800825a   0x0800825a   0x0000002e   Code   RO          559    .text               mf_w.l(dscalb.o)
    0x08008288   0x08008288   0x00000024   Code   RO          561    .text               mc_w.l(init.o)
    0x080082ac   0x080082ac   0x0000001e   Code   RO          563    .text               mc_w.l(llshl.o)
    0x080082ca   0x080082ca   0x00000020   Code   RO          565    .text               mc_w.l(llushr.o)
    0x080082ea   0x080082ea   0x00000024   Code   RO          567    .text               mc_w.l(llsshr.o)
    0x0800830e   0x0800830e   0x000000a2   Code   RO          569    .text               mf_w.l(dsqrt.o)
    0x080083b0   0x080083b0   0x0000005e   Code   RO          579    .text               mc_w.l(__dclz77c.o)
    0x0800840e   0x0800840e   0x00000028   Code   RO          512    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08008436   0x08008436   0x000000aa   Code   RO          514    i.__kernel_poly     m_ws.l(poly.o)
    0x080084e0   0x080084e0   0x00000006   Code   RO          499    i.__mathlib_dbl_infnan  m_ws.l(dunder.o)
    0x080084e6   0x080084e6   0x00000004   Code   RO          500    i.__mathlib_dbl_infnan2  m_ws.l(dunder.o)
    0x080084ea   0x080084ea   0x0000000c   Code   RO          501    i.__mathlib_dbl_invalid  m_ws.l(dunder.o)
    0x080084f6   0x080084f6   0x00000002   PAD
    0x080084f8   0x080084f8   0x00000010   Code   RO          504    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x08008508   0x08008508   0x0000000e   Code   RO          573    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08008516   0x08008516   0x00000002   Code   RO          574    i.__scatterload_null  mc_w.l(handlers.o)
    0x08008518   0x08008518   0x0000000e   Code   RO          575    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08008526   0x08008526   0x00000002   PAD
    0x08008528   0x08008528   0x0000000c   Code   RO          545    i.__set_errno       mc_w.l(errno.o)
    0x08008534   0x08008534   0x00000270   Code   RO          434    i.asin              m_ws.l(asin.o)
    0x080087a4   0x080087a4   0x00000220   Code   RO          489    i.atan              m_ws.l(atan.o)
    0x080089c4   0x080089c4   0x0000019c   Code   RO          444    i.atan2             m_ws.l(atan2.o)
    0x08008b60   0x08008b60   0x0000004c   Code   RO          518    i.sqrt              m_ws.l(sqrt.o)
    0x08008bac   0x08008bac   0x0000230e   Data   RO          307    .constdata          oled.o
    0x0800aeba   0x0800aeba   0x00000bf6   Data   RO          382    .constdata          anbt_dmp_driver.o
    0x0800bab0   0x0800bab0   0x00000050   Data   RO          400    .constdata          anbt_dmp_mpu6050.o
    0x0800bb00   0x0800bb00   0x00000050   Data   RO          435    .constdata          m_ws.l(asin.o)
    0x0800bb50   0x0800bb50   0x00000098   Data   RO          490    .constdata          m_ws.l(atan.o)
    0x0800bbe8   0x0800bbe8   0x00000008   Data   RO          516    .constdata          m_ws.l(qnan.o)
    0x0800bbf0   0x0800bbf0   0x00000020   Data   RO          571    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800bc10, Size: 0x00001520, Max: 0x00005000, ABSOLUTE, COMPRESSED[0x00000448])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x0000000e   Data   RW            2    .data               main.o
    0x2000000e   COMPRESSED   0x00000002   PAD
    0x20000010   COMPRESSED   0x00000058   Data   RW          110    .data               control.o
    0x20000068   COMPRESSED   0x00000021   Data   RW          140    .data               data_transfer.o
    0x20000089   COMPRESSED   0x00000003   PAD
    0x2000008c   COMPRESSED   0x0000070f   Data   RW          161    .data               ui.o
    0x2000079b   COMPRESSED   0x00000001   PAD
    0x2000079c   COMPRESSED   0x00000004   Data   RW          179    .data               delay.o
    0x200007a0   COMPRESSED   0x00000017   Data   RW          251    .data               led.o
    0x200007b7   COMPRESSED   0x00000001   PAD
    0x200007b8   COMPRESSED   0x00000029   Data   RW          356    .data               ultrasonic.o
    0x200007e1   COMPRESSED   0x00000003   PAD
    0x200007e4   COMPRESSED   0x00000068   Data   RW          401    .data               anbt_dmp_mpu6050.o
    0x2000084c   COMPRESSED   0x00000004   Data   RW          546    .data               mc_w.l(errno.o)
    0x20000850        -       0x0000006c   Zero   RW          109    .bss                control.o
    0x200008bc        -       0x00000040   Zero   RW          139    .bss                data_transfer.o
    0x200008fc        -       0x00000400   Zero   RW          269    .bss                stmflash.o
    0x20000cfc        -       0x00000400   Zero   RW          306    .bss                oled.o
    0x200010fc        -       0x00000010   Zero   RW          381    .bss                anbt_dmp_driver.o
    0x2000110c        -       0x00000010   Zero   RW          399    .bss                anbt_dmp_mpu6050.o
    0x2000111c   COMPRESSED   0x00000004   PAD
    0x20001120        -       0x00000400   Zero   RW          373    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      3656        126       3062          0         16      11644   anbt_dmp_driver.o
      6864        158         80        104         16      19079   anbt_dmp_mpu6050.o
       772        116          0         88        108       2734   control.o
      2148         94          0         33         64       5988   data_transfer.o
       176          8          0          4          0       1059   delay.o
       104          4          0          0          0        931   dma.o
       660         72          0         23          0       1970   led.o
       668        118          0         14          0     211612   main.o
       404         26          0          0          0        869   motor.o
      1692         46          0          0          0       5539   mpu_i2c.o
      3752         50       8974          0       1024      12400   oled.o
        28          4        304          0       1024        776   startup_stm32f10x_hd.o
       560         22          0          0       1024       3716   stmflash.o
       708         50          0          0          0       2823   sys.o
      7520        664          0       1807          0       8505   ui.o
       684         50          0         41          0       1770   ultrasonic.o
       560         38          0          0          0       3593   usart.o

    ----------------------------------------------------------------------
     30956       <USER>      <GROUP>       2124       3280     295008   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         0          0          0         10          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       624         52         80          0          0        168   asin.o
       544         70        152          0          0        124   atan.o
       412         38          0          0          0        144   atan2.o
        38          6          0          0          0        272   dunder.o
        40          0          0          0          0         68   fpclassify.o
       170          0          0          0          0         96   poly.o
         0          0          8          0          0          0   qnan.o
        76          0          0          0          0         84   sqrt.o
        94          0          0          0          0          0   __dclz77c.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        98          0          0          0          0         84   ldiv.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0        108   memseta.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        20          0          0          0          0         68   cfcmple.o
        20          0          0          0          0         68   cfrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
       228          0          0          0          0         96   dmul.o
        46          0          0          0          0         80   dscalb.o
       162          0          0          0          0        100   dsqrt.o
        38          0          0          0          0         68   f2d.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        50          0          0          0          0         68   ffixi.o
        40          0          0          0          0         68   ffixui.o
        18          0          0          0          0         68   fflti.o
        10          0          0          0          0         68   ffltui.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
      4496        <USER>        <GROUP>          4          0       3532   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1904        166        240          0          0        956   m_ws.l
       552         22          0          4          0        704   mc_w.l
      2036          0          0          0          0       1872   mf_w.l

    ----------------------------------------------------------------------
      4496        <USER>        <GROUP>          4          0       3532   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     35452       1834      12692       2128       3280     295600   Grand Totals
     35452       1834      12692       1096       3280     295600   ELF Image Totals (compressed)
     35452       1834      12692       1096          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                48144 (  47.02kB)
    Total RW  Size (RW Data + ZI Data)              5408 (   5.28kB)
    Total ROM Size (Code + RO Data + RW Data)      49240 (  48.09kB)

==============================================================================

