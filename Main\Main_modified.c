/************************************/
/************************************/
#include "sys.h"   			//包含系统头文件
#include "delay.h" 			//包含延时头文件
#include "usart.h"	
#include "anbt_dmp_mpu6050.h"	
#include "encoder.h"
#include "control.h"
#include "motor.h"
#include "led.h"
#include "DMA.h"
#include "data_transfer.h"
#include "camera_control.h"  // 新增：相机控制头文件
#include "control.h"
#include "stmflash.h"
#include "oled.h"
#include "oled_user.h"
#include "ADC.h"
#include "UI.h"
#include "ultrasonic.h"

unsigned char PS2_Flag=0;

/************************************/
/*               主程序             */
/************************************/  
int main(void)
{	
    Stm32_Clock_Init(9);			//系统时钟设置，9*8M=72M
    Delay_Init(72);					//延时初始化，主频为72M
    JTAG_Set(JTAG_SWD_DISABLE); 
    JTAG_Set(SWD_ENABLE);
    Motor_Init();					//PWM输出初始化
    LED_Init();						//初始化LED，按键
    OLED_Init();					//初始化OLED，
    OLED_Refresh_AllGDRAM(); 		//刷新显示
    
    // 串口初始化
    USART1_Init(72,9600);			//初始化串口1（调试用）
    USART3_Init(36,9600);			//初始化串口3（相机数据）
    
    // 移除原有的DMA配置，改用中断接收
    // MYDMA_Config(DMA1_Channel3,(u32)&USART3->DR,(u32)Blue_dat,UART_RECV_LEN);
    
    // 使能串口3接收中断
    USART_ITConfig(USART3, USART_IT_RXNE, ENABLE);
    NVIC_EnableIRQ(USART3_IRQn);
    
    // 保留串口1的DMA配置（如果需要）
    MYDMA_Config(DMA1_Channel5,(u32)&USART1->DR,(u32)Blue_dat,UART_RECV_LEN);
    
    //	Adc_Init();					//初始化ADC，检测电池电压
    PID_Init();						//PID数值初始化	
    delay_ms(1000);					//延时
    //	Encoder_Init();				//初始化编码器
    AnBT_DMP_MPU6050_Init();		//6050DMP初始化,不校准						
    LED_state=1; 
    
    // 初始化数据
    Blue_RC[0]=0;Blue_RC[1]=0;
    WorkMode=0x00;
    UI_Display_Flag=1;				//标记改变界面	
    Flash_Read_Language();			//界面语言设置
    Flash_Read_VoltWarn();			//读取报警电压
    
    // 初始化相机数据接收状态
    reset_receive_state();
    
    // 电机停止
    ROLL_MOTOR_STOP=1;YAW_MOTOR_STOP=1;
    
    while(1) 
    {	
        if(Control_Flag)
        {	
            switch(WorkMode)
            {
                ////////////////////待机模式////////////////////////
                case 0x00:Standby_Mode();break;
                ////////////////////平衡模式////////////////////////
                case 0x01:Balance_Mode();break;
                ////////////////////设置模式////////////////////////
                case 0x02:Settings_Mode();break;	
                ////////////////////PID设置////////////////////////
                case 0x03:PID_Settings();break;
                /////////////////////安全角度设置///////////////////
                case 0x04:SafeAngle_Settings();break;
                /////////////////////角度校准//////////////////////
                case 0x05:AdjustAngle_Settings();break;
                /////////////////////速度设定//////////////////////	
                case 0x06:break;
                /////////////////////语言设置//////////////////////		
                case 0x07:LanguageSettings_Mode();break;
                /////////////////////出厂设置//////////////////////		
                case 0x08:FactorySettings_Mode();break;						
                /////////////////////其他设置//////////////////////		
                case 0x09:Other_Settings_Mode();break;
                /////////////////////电压低报警////////////////////						
                //				case 0x0A:VoltWarn_Mode();break;				
                default:			break;
            }
            Control_Flag=0;
            if(AdjustAngle_Flag>0)	AdjustAngle_Flag-=5;	
            //			Send_Data();	//发送数据
            //			Volt=Get_Adc(7);Volt=Volt*33*6/4096;	//检测电压
            //			if(WorkMode==0x00 || WorkMode==0x01)
            //			{
            //				if( Volt<VoltLowWarn) 
            //				{
            //					if(VoltLowTimes<600)VoltLowTimes++;
            //					else {WorkMode=0x0A;ARMED=0;time_tick=0;}
            //				}
            //				else	VoltLowTimes=0;
            //			}
        }
    }											    
}

unsigned char displaystick=0;
short int RC_Last=0;

/**
 * @brief 定时中断处理函数 - 主控制循环
 * @note 每5ms执行一次，负责数据处理和控制算法
 */
void EXTI15_10_IRQHandler(void)
{	
    // 处理相机数据
    vData_Get();
    
    // 保留蓝牙数据处理（作为备用控制方式）
    BlueData_Receive_Anl();
    
    // 读取陀螺仪数据和解算欧拉角
    MPU6050_Pose();
    
    if(PickUp_Flag<10)				//平衡车未拿起
    {			
        if(MPU6050_accel[2]>22000 && ARMED==1) PickUp_Flag++;
        else	PickUp_Flag=0;	
        
        // 智能控制模式选择
        if(Camera_HasNewData() && Camera_IsDataValid())
        {
            // 优先使用相机数据进行目标跟踪控制
            CONTROL_Image_Target(Pitch, Usartdata.x, Usartdata.y);
            
            #ifdef DEBUG_CAMERA_DATA
            // 调试信息输出
            Print_Debug_Info(Usartdata.x, Usartdata.y);
            #endif
        }
        else
        {
            // 相机数据无效时，使用原有的蓝牙遥控数据
            CONTROL(Pitch, Blue_RC[0]*4, Blue_RC[1]*2);
        }
    }
    else
    {
        // 设备被拿起时停止所有电机
        PID_Speed.iout=0;PID_PIT.iout=0;PID_YAW.iout=0;
        PID_Speed.pout=0;
        Set_Motor(0,0);
        if(PickUp_Flag<100) PickUp_Flag++;
        else		//拿起0.5秒之后
        {
            if(Pitch<8.0 && Pitch>-8.0)	//在±8度之间
            {
                if(MPU6050_accel[2]>28000) PickUp_Flag=0;
            }
        }
    }
    
    RC_Last=Blue_RC[0];	
    LED_Ring_Control();				//控制LED和蜂鸣器
    
    time_tick++;	
    if(time_tick>=1000)time_tick=0;
    if(time_tick%2 == 0)//10ms  --  100HZ进行一次
    {	
        Send_Attitude=1;	PS2_Flag=1;	
    }
    Control_Flag=1;	
    
    Key_Check();	
    
    OLED_Refresh_GDRAM(displaystick++);	//刷新显示，OLED屏分8次显示，5ms*8=40ms完成一次刷新
    if(displaystick==8)displaystick=0;	
    EXTI->PR=1<<12;  //清除LINE3上的中断标志位
}
