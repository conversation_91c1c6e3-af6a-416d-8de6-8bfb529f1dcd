@echo off
echo ========================================
echo    修复编译错误脚本
echo ========================================
echo.

echo 第一步：替换有问题的UI文件...

:: 使用简化版UI文件替换有问题的版本
if exist "User\UI_enhanced_simple.c" (
    copy "User\UI_enhanced_simple.c" "User\UI_enhanced.c" >nul
    echo ✓ 已替换为简化版UI_enhanced.c
) else (
    echo ✗ 未找到简化版UI文件
)

if exist "User\UI_enhanced_simple.h" (
    copy "User\UI_enhanced_simple.h" "User\UI_enhanced.h" >nul
    echo ✓ 已替换为简化版UI_enhanced.h
) else (
    echo ✗ 未找到简化版UI头文件
)

echo.
echo 第二步：检查关键文件...

if exist "User\camera_control.c" (
    echo ✓ camera_control.c 存在
) else (
    echo ✗ camera_control.c 不存在
)

if exist "User\camera_control.h" (
    echo ✓ camera_control.h 存在
) else (
    echo ✗ camera_control.h 不存在
)

if exist "Main\Main_modified.c" (
    echo ✓ Main_modified.c 存在
) else (
    echo ✗ Main_modified.c 不存在
)

echo.
echo ========================================
echo 修复完成！现在可以重新编译项目
echo ========================================
echo.
echo 接下来的操作：
echo 1. 在Keil中移除原有的UI_enhanced.c（如果已添加）
echo 2. 重新添加修复后的UI_enhanced.c到USER组
echo 3. 按F7重新编译项目
echo 4. 检查编译结果
echo.
echo 如果仍有编译错误，请检查：
echo - 包含路径是否正确设置
echo - 所有必要的头文件是否存在
echo - STM32标准库是否正确配置
echo ========================================
echo.
pause
