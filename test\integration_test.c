/**
 * @file integration_test.c
 * @brief 相机云台集成测试文件
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "camera_control.h"
#include "data_transfer.h"
#include "control.h"
#include "motor.h"
#include "anbt_dmp_mpu6050.h"

/* ======================== 测试配置 ======================== */

#define TEST_MODE_CAMERA_DATA    1  // 测试相机数据接收
#define TEST_MODE_COORDINATE     2  // 测试坐标转换
#define TEST_MODE_CONTROL        3  // 测试控制算法
#define TEST_MODE_INTEGRATION    4  // 集成测试

// 当前测试模式
#define CURRENT_TEST_MODE TEST_MODE_INTEGRATION

/* ======================== 测试数据 ======================== */

// 模拟相机数据包
uint8_t test_camera_packet[] = {
    0xFF, 0xF8,           // 帧头
    0x40, 0x01,           // X坐标: 320 (屏幕中心)
    0xF0, 0x00,           // Y坐标: 240 (屏幕中心)
    0x00,                 // X误差: 0
    0x00,                 // Y误差: 0
    0x01,                 // 标识位
    0xFE                  // 帧尾
};

// 测试坐标点
int16_t test_coordinates[][2] = {
    {320, 240},  // 屏幕中心
    {400, 200},  // 右上
    {240, 280},  // 左下
    {320, 180},  // 上方
    {320, 300}   // 下方
};

/* ======================== 测试函数 ======================== */

/**
 * @brief 测试相机数据接收
 */
void Test_Camera_Data_Reception(void)
{
    printf("=== 测试相机数据接收 ===\r\n");
    
    // 模拟接收数据包
    Camera_ParseData(test_camera_packet);
    
    // 检查解析结果
    CameraData_t* data = Camera_GetData();
    printf("解析结果:\r\n");
    printf("  X坐标: %d\r\n", data->center_x);
    printf("  Y坐标: %d\r\n", data->center_y);
    printf("  X误差: %d\r\n", data->err_x);
    printf("  Y误差: %d\r\n", data->err_y);
    printf("  有效性: %s\r\n", data->valid ? "有效" : "无效");
    
    // 测试数据转换
    vData_Get();
    printf("转换后数据:\r\n");
    printf("  Usartdata.x: %d\r\n", Usartdata.x);
    printf("  Usartdata.y: %d\r\n", Usartdata.y);
    
    printf("相机数据接收测试完成\r\n\r\n");
}

/**
 * @brief 测试坐标转换算法
 */
void Test_Coordinate_Conversion(void)
{
    printf("=== 测试坐标转换算法 ===\r\n");
    
    for(int i = 0; i < 5; i++)
    {
        // 转换坐标
        vImage_To_Gimbal_Angle(test_coordinates + i, 1);
        
        printf("坐标点 %d: (%d, %d)\r\n", i+1, 
               test_coordinates[i][0], test_coordinates[i][1]);
        printf("  -> PITCH角度: %.2f°\r\n", target_angle[0]);
        printf("  -> YAW角度: %.2f°\r\n", target_angle[1]);
        printf("\r\n");
    }
    
    printf("坐标转换算法测试完成\r\n\r\n");
}

/**
 * @brief 测试控制算法
 */
void Test_Control_Algorithm(void)
{
    printf("=== 测试控制算法 ===\r\n");
    
    // 模拟当前姿态
    float current_pitch = 0.0f;
    
    // 测试不同目标点的控制输出
    for(int i = 0; i < 3; i++)
    {
        printf("测试目标点 %d: (%d, %d)\r\n", i+1,
               test_coordinates[i][0], test_coordinates[i][1]);
        
        // 执行控制算法（不实际输出到电机）
        ARMED = 0;  // 禁用电机输出
        CONTROL_Image_Target(current_pitch, 
                           test_coordinates[i][0], 
                           test_coordinates[i][1]);
        
        printf("  PITCH PID输出: %.2f\r\n", PID_PIT.OUT);
        printf("  YAW PID输出: %.2f\r\n", PID_YAW.OUT);
        printf("  目标角度: P=%.2f°, Y=%.2f°\r\n", 
               target_angle[0], target_angle[1]);
        printf("\r\n");
        
        delay_ms(100);
    }
    
    printf("控制算法测试完成\r\n\r\n");
}

/**
 * @brief 集成测试
 */
void Test_Integration(void)
{
    printf("=== 集成测试 ===\r\n");
    
    // 初始化系统
    reset_receive_state();
    PID_Init();
    
    printf("系统初始化完成\r\n");
    
    // 模拟完整的数据流程
    for(int cycle = 0; cycle < 5; cycle++)
    {
        printf("\r\n--- 测试周期 %d ---\r\n", cycle + 1);
        
        // 1. 模拟接收相机数据
        test_camera_packet[2] = (test_coordinates[cycle % 5][0]) & 0xFF;
        test_camera_packet[3] = (test_coordinates[cycle % 5][0]) >> 8;
        test_camera_packet[4] = (test_coordinates[cycle % 5][1]) & 0xFF;
        test_camera_packet[5] = (test_coordinates[cycle % 5][1]) >> 8;
        
        Camera_ParseData(test_camera_packet);
        vData_Get();
        
        // 2. 检查数据有效性
        if(Camera_HasNewData() && Camera_IsDataValid())
        {
            printf("接收到有效数据: (%d, %d)\r\n", Usartdata.x, Usartdata.y);
            
            // 3. 执行控制算法
            ARMED = 0;  // 测试模式，不实际驱动电机
            CONTROL_Image_Target(0.0f, Usartdata.x, Usartdata.y);
            
            // 4. 输出结果
            printf("控制输出: PITCH=%.2f, YAW=%.2f\r\n", 
                   PID_PIT.OUT, PID_YAW.OUT);
            printf("目标角度: P=%.2f°, Y=%.2f°\r\n", 
                   target_angle[0], target_angle[1]);
        }
        else
        {
            printf("数据无效或超时\r\n");
        }
        
        delay_ms(200);
    }
    
    printf("\r\n集成测试完成\r\n");
}

/**
 * @brief 性能测试
 */
void Test_Performance(void)
{
    printf("=== 性能测试 ===\r\n");
    
    uint32_t start_time, end_time;
    
    // 测试数据解析性能
    start_time = time_tick;
    for(int i = 0; i < 1000; i++)
    {
        Camera_ParseData(test_camera_packet);
    }
    end_time = time_tick;
    printf("数据解析性能: %d次/秒\r\n", 1000 * 200 / (end_time - start_time));
    
    // 测试坐标转换性能
    start_time = time_tick;
    for(int i = 0; i < 1000; i++)
    {
        vImage_To_Gimbal_Angle(test_coordinates, 1);
    }
    end_time = time_tick;
    printf("坐标转换性能: %d次/秒\r\n", 1000 * 200 / (end_time - start_time));
    
    printf("性能测试完成\r\n\r\n");
}

/**
 * @brief 主测试函数
 */
void Run_Integration_Tests(void)
{
    printf("\r\n========== 相机云台集成测试 ==========\r\n");
    printf("测试模式: %d\r\n", CURRENT_TEST_MODE);
    printf("系统时钟: %dMHz\r\n", SystemCoreClock / 1000000);
    printf("=====================================\r\n\r\n");
    
    switch(CURRENT_TEST_MODE)
    {
        case TEST_MODE_CAMERA_DATA:
            Test_Camera_Data_Reception();
            break;
            
        case TEST_MODE_COORDINATE:
            Test_Coordinate_Conversion();
            break;
            
        case TEST_MODE_CONTROL:
            Test_Control_Algorithm();
            break;
            
        case TEST_MODE_INTEGRATION:
            Test_Integration();
            break;
            
        default:
            // 运行所有测试
            Test_Camera_Data_Reception();
            Test_Coordinate_Conversion();
            Test_Control_Algorithm();
            Test_Integration();
            Test_Performance();
            break;
    }
    
    printf("========== 测试完成 ==========\r\n\r\n");
}

/**
 * @brief 测试初始化
 */
void Test_Init(void)
{
    // 初始化串口用于调试输出
    USART1_Init(72, 115200);
    
    // 初始化相机控制系统
    reset_receive_state();
    
    printf("测试环境初始化完成\r\n");
}
