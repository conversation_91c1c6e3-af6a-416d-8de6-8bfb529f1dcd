#ifndef _CAMERA_CONTROL_H_
#define _CAMERA_CONTROL_H_

#include "sys.h"
#include "data_transfer.h"

/* ======================== 宏定义常量 ======================== */

// 接收缓冲区大小
#define RX_BUFFER_SIZE 64
#define PACKET_SIZE 10
#define POINT_CNT 1

// 坐标转换相关参数
#define ImageToActual (1.93f)    // 像素到实际距离转换系数 (mm/pixel)
#define Distance (1000.0f)       // 摄像头到屏幕距离 (mm)
#define Alpha (0.995f)           // 坐标转换补偿系数

// 屏幕参数
#define SCREEN_CENTER_X (320)    // 屏幕中心X坐标
#define SCREEN_CENTER_Y (240)    // 屏幕中心Y坐标

// 角度限制
#define MAX_PITCH_ANGLE (30.0f)  // PITCH最大角度
#define MIN_PITCH_ANGLE (-30.0f) // PITCH最小角度
#define MAX_YAW_ANGLE (60.0f)    // YAW最大角度
#define MIN_YAW_ANGLE (-60.0f)   // YAW最小角度

// 控制参数
#define YAW_SPEED_FACTOR (8.0f)  // YAW角度转角速度系数
#define DATA_TIMEOUT_MS (500)    // 数据超时时间(ms)

// 调试开关
// #define DEBUG_CAMERA_DATA     // 取消注释以启用调试输出

// 数学工具宏
#define _Round(input) ((int16_t)((input)>0.0f?(input)+0.5f:(input)-0.5f))
#define _Sign(input) ((input)!=0?((input)>0?1:-1):0)
#define _Abs(input) ((input)<0?-(input):(input))

/* ======================== 数据结构定义 ======================== */

// 相机数据结构
typedef struct {
    uint16_t center_x;      // 中心X坐标
    uint16_t center_y;      // 中心Y坐标
    int8_t err_x;           // X方向误差
    int8_t err_y;           // Y方向误差
    uint8_t valid;          // 数据有效标志
} CameraData_t;

// 数据点结构
typedef struct {
    int16_t x;
    int16_t y;
    uint8_t num;
} _DATAPoint;

/* ======================== 全局变量声明 ======================== */

// 相机数据相关变量
extern _DATAPoint Usartdata;
extern int16_t usart_point[1][2];
extern float target_angle[2];  // [pitch, yaw]

/* ======================== 函数声明 ======================== */

// 串口中断处理
void USART3_IRQHandler(void);

// 数据解析处理
void Camera_ParseData(uint8_t *data);
void vData_Get(void);

// 坐标转换算法
void vImage_To_Gimbal_Angle(int16_t Image[][2], uint8_t cnt);

// 控制算法
void CONTROL_Image_Target(float pit_now, int16_t target_x, int16_t target_y);

// 辅助函数
CameraData_t* Camera_GetData(void);
uint8_t Camera_HasNewData(void);
uint8_t Camera_IsDataValid(void);
void reset_receive_state(void);

// 调试和监控函数
void Get_Target_Angles(float *pitch_angle, float *yaw_angle);
void Print_Debug_Info(int16_t x, int16_t y);

// 兼容性函数（保持原有接口）
void vSingle_Point_Receive(void);

#endif /* _CAMERA_CONTROL_H_ */
