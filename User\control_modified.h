#ifndef __CONTROL_H__
#define __CONTROL_H__

#include "sys.h"

/* ======================== PID结构体定义 ======================== */

typedef struct
{
	float Pdat;
	float Idat;
	float Ddat;
	float Error;
	float Last_Error;
	float pout;
	float iout;
	float dout;
	float OUT;
} PID;

/* ======================== 全局变量声明 ======================== */

extern int MotorL_PWM;	//左电机PWM
extern int MotorR_PWM;	//右电机PWM

extern u8 ARMED;
extern PID PID_Speed; 	//定义速度控制PID
extern PID PID_PIT; 		//定义平衡控制PID
extern PID PID_YAW; 		//定义转向控制PID
extern short int Front_SafeAngle;	//前倾安全角度
extern short int Back_SafeAngle;		//后倾安全角度

extern float yaw_gyro_last;
extern float pit_gyro_now,pit_gyro_Last1,pit_gyro_Last2;
extern float yaw_gyro_now,yaw_gyro_last0,yaw_gyro_last1;
extern unsigned char Control_stik;

extern unsigned char PickUp_Flag;	//拿起标志位

// 新增：平衡系数相关变量
extern float Speed_Xishu;
extern float Banlan_Xishu;

/* ======================== 函数声明 ======================== */

// 原有控制函数
void PID_Init(void);
void CONTROL(float pit_now,int Speed_tar,s16 yaw_gyro_tar);

// 新增：相机控制函数声明
void CONTROL_Image_Target(float pit_now, int16_t target_x, int16_t target_y);

#endif
