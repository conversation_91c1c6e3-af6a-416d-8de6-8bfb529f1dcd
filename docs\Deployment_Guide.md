# 相机云台系统部署指南

## 1. 文件部署清单

### 1.1 新增文件
```
User/
├── camera_control.c        # 相机控制核心文件
├── camera_control.h        # 相机控制头文件
├── data_transfer_modified.c # 修改后的数据传输文件
├── data_transfer_modified.h # 修改后的数据传输头文件
└── control_modified.h      # 修改后的控制头文件

Main/
└── Main_modified.c         # 修改后的主程序文件

test/
└── integration_test.c      # 集成测试文件

docs/
├── Camera_Gimbal_Integration_Guide.md  # 集成指南
└── Deployment_Guide.md     # 部署指南（本文件）
```

### 1.2 需要修改的原有文件
```
User/
├── data_transfer.c         # 需要替换为 data_transfer_modified.c
├── data_transfer.h         # 需要替换为 data_transfer_modified.h
└── control.h              # 需要替换为 control_modified.h

Main/
└── Main.c                 # 需要替换为 Main_modified.c
```

## 2. 部署步骤

### 2.1 备份原有文件
```bash
# 创建备份目录
mkdir backup

# 备份原有文件
cp User/data_transfer.c backup/
cp User/data_transfer.h backup/
cp User/control.h backup/
cp Main/Main.c backup/
```

### 2.2 部署新文件
```bash
# 复制新增文件
cp User/camera_control.c User/
cp User/camera_control.h User/

# 替换修改后的文件
cp User/data_transfer_modified.c User/data_transfer.c
cp User/data_transfer_modified.h User/data_transfer.h
cp User/control_modified.h User/control.h
cp Main/Main_modified.c Main/Main.c
```

### 2.3 项目配置修改

#### Keil项目设置
1. 打开 `TEST.uvprojx` 项目文件
2. 在 `USER` 组中添加 `camera_control.c`
3. 确保包含路径包含 `User` 目录
4. 重新编译项目

#### 编译选项
```c
// 在项目设置中添加预编译宏（可选）
#define DEBUG_CAMERA_DATA    // 启用调试输出
#define USE_CAMERA_CONTROL   // 启用相机控制功能
```

## 3. 硬件连接验证

### 3.1 串口连接检查
- **串口3 (USART3)**: 
  - TX: PB10
  - RX: PB11
  - 波特率: 9600
  - 数据位: 8
  - 停止位: 1
  - 校验位: 无

### 3.2 电机连接检查
- **PITCH电机 (ROLL_MOTOR)**:
  - PWM: TIM1_CH1 (PA8)
  - DIR: PB13
  - STOP: PB12

- **YAW电机**:
  - PWM: TIM1_CH4 (PA11)
  - DIR: PB14
  - STOP: PB15

### 3.3 传感器连接检查
- **MPU6050**: I2C接口
- **OLED显示屏**: SPI接口
- **LED指示灯**: 相应GPIO

## 4. 软件配置

### 4.1 参数配置
在 `camera_control.h` 中调整以下参数：

```c
// 坐标转换参数（根据实际相机调整）
#define ImageToActual (1.93f)    // 像素到实际距离转换系数
#define Distance (1000.0f)       // 相机到目标距离
#define Alpha (0.995f)           // 补偿系数

// 屏幕参数（根据实际分辨率调整）
#define SCREEN_CENTER_X (320)    // 屏幕中心X坐标
#define SCREEN_CENTER_Y (240)    // 屏幕中心Y坐标

// 角度限制（根据机械限位调整）
#define MAX_PITCH_ANGLE (30.0f)
#define MIN_PITCH_ANGLE (-30.0f)
#define MAX_YAW_ANGLE (60.0f)
#define MIN_YAW_ANGLE (-60.0f)

// 控制参数（根据响应速度调整）
#define YAW_SPEED_FACTOR (8.0f)  // YAW角速度转换系数
```

### 4.2 PID参数调整
可能需要重新调整PID参数以适应新的控制算法：

```c
// 在control.c中的参考参数
// PITCH轴PID: P=379, I=0, D=184
// YAW轴PID: P=16, I=0, D=0
```

## 5. 测试验证

### 5.1 编译测试
```bash
# 编译项目
# 检查是否有编译错误或警告
# 确保所有依赖文件都正确包含
```

### 5.2 功能测试

#### 基础功能测试
1. **串口通信测试**
   - 发送测试数据包
   - 验证数据解析正确性
   - 检查LED指示灯闪烁

2. **坐标转换测试**
   - 输入已知坐标
   - 验证角度计算结果
   - 检查角度限制功能

3. **控制算法测试**
   - 禁用电机输出 (`ARMED=0`)
   - 验证PID计算结果
   - 检查控制逻辑

#### 集成测试
```c
// 在main函数中添加测试代码
#ifdef ENABLE_INTEGRATION_TEST
    Test_Init();
    Run_Integration_Tests();
#endif
```

### 5.3 实际运行测试
1. **安全测试**
   - 确保拿起检测功能正常
   - 验证角度限制保护
   - 测试紧急停止功能

2. **性能测试**
   - 测试响应时间
   - 验证跟踪精度
   - 检查长时间运行稳定性

## 6. 故障排除

### 6.1 编译错误
- **未找到头文件**: 检查包含路径设置
- **函数未定义**: 确保所有源文件都已添加到项目
- **重复定义**: 检查是否有重复的全局变量定义

### 6.2 运行时错误
- **无相机数据**: 检查串口连接和波特率
- **控制异常**: 验证PID参数和角度限制
- **电机不响应**: 检查ARMED标志和电机连接

### 6.3 性能问题
- **响应慢**: 调整YAW_SPEED_FACTOR参数
- **振荡**: 重新调整PID参数
- **精度差**: 校准ImageToActual和Distance参数

## 7. 维护说明

### 7.1 参数标定
定期检查和校准以下参数：
- 相机标定参数
- PID控制参数
- 机械限位角度

### 7.2 软件更新
- 保持代码版本管理
- 记录参数修改历史
- 定期备份配置文件

### 7.3 硬件维护
- 检查连接线路
- 清洁传感器
- 润滑机械部件

## 8. 技术支持

### 8.1 调试工具
- 串口调试助手
- 逻辑分析仪
- 示波器

### 8.2 日志记录
启用调试输出以记录系统运行状态：
```c
#define DEBUG_CAMERA_DATA    // 相机数据调试
#define DEBUG_CONTROL_OUTPUT // 控制输出调试
#define DEBUG_PID_VALUES     // PID数值调试
```

### 8.3 联系方式
如遇到技术问题，请提供：
- 详细错误描述
- 系统配置信息
- 调试日志输出
- 硬件连接图
