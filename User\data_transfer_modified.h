#ifndef _DATA_TRANSFER_H_
#define _DATA_TRANSFER_H_
#include "sys.h"
#include "control.h"
#include "led.h"
#include "usart.h"
#include "delay.h"

#define DATA_TRANSFER_USE_USART

// 原有蓝牙相关定义
#define UART_RECV_LEN	32

// 相机数据相关定义
#define RX_BUFFER_SIZE 64
#define PACKET_SIZE 10
#define POINT_CNT 1

/* ======================== 坐标转换相关参数 ======================== */
// 坐标转换相关参数
#define ImageToActual (1.93f)    // 像素到实际距离转换系数 (mm/pixel)
#define Distance (1000.0f)       // 摄像头到屏幕距离 (mm)
#define Alpha (0.995f)           // 坐标转换补偿系数

// 屏幕参数
#define SCREEN_CENTER_X (320)    // 屏幕中心X坐标
#define SCREEN_CENTER_Y (240)    // 屏幕中心Y坐标

// 角度限制
#define MAX_PITCH_ANGLE (30.0f)  // PITCH最大角度
#define MIN_PITCH_ANGLE (-30.0f) // PITCH最小角度
#define MAX_YAW_ANGLE (60.0f)    // YAW最大角度
#define MIN_YAW_ANGLE (-60.0f)   // YAW最小角度

// 控制参数
#define YAW_SPEED_FACTOR (8.0f)  // YAW角度转角速度系数

// 数学工具宏
#define _Round(input) ((int16_t)((input)>0.0f?(input)+0.5f:(input)-0.5f))
#define _Sign(input) ((input)!=0?((input)>0?1:-1):0)
#define _Abs(input) ((input)<0?-(input):(input))

/* ======================== 数据结构定义 ======================== */

// 相机数据结构
typedef struct {
    uint16_t center_x;      // 中心X坐标
    uint16_t center_y;      // 中心Y坐标
    int8_t err_x;           // X方向误差
    int8_t err_y;           // Y方向误差
    uint8_t valid;          // 数据有效标志
} CameraData_t;

// 数据点结构
typedef struct {
    int16_t x;
    int16_t y;
    uint8_t num;
} _DATAPoint;

/* ======================== 全局变量声明 ======================== */

// 原有蓝牙相关变量
extern unsigned char Blue_dat[UART_RECV_LEN];
extern short int Blue_RC[3];

// 相机数据相关变量
extern CameraData_t camera_data;
extern _DATAPoint Usartdata;
extern int16_t usart_point[1][2];
extern uint8_t rx_buffer[PACKET_SIZE];
extern uint8_t rx_index;

// 目标角度变量
extern float target_angle[2];  // [pitch, yaw]

// 其他变量
extern int LeftKnob,RightKnob;
extern unsigned char Send_Attitude;
extern unsigned char Send_Senser;
extern unsigned char Send_RCData;
extern unsigned char Send_PID1;
extern unsigned int time_tick;
extern unsigned char Control_Flag;

/* ======================== 函数声明 ======================== */

// 原有蓝牙数据处理函数
void Data_Receive_Anl(void);
void BlueData_Receive_Anl(void);

// 相机数据处理函数
void Camera_ParseData(uint8_t *data);
void vData_Get(void);
void vSingle_Point_Receive(void);
CameraData_t* Camera_GetData(void);
uint8_t Camera_HasNewData(void);
void reset_receive_state(void);

// 坐标转换函数
void vImage_To_Gimbal_Angle(int16_t Image[][2], uint8_t cnt);
void CONTROL_Image_Target(float pit_now, int16_t target_x, int16_t target_y);

// 调试和监控函数
void Get_Target_Angles(float *pitch_angle, float *yaw_angle);
void Print_Debug_Info(int16_t x, int16_t y);
uint8_t Camera_IsDataValid(void);

// 数据发送函数
void Data_Send_Attitude(void);	
void Data_Send_PID1(void);
void Send_Data(void);

// Flash存储函数
#define GYRO_OFFSET_FLASHADR	0
#define ACC_OFFSET_FLASHADR		20
#define PID_FLASHADR					40
#define SAFEANGLE_FLASHADR		60
#define LANGUAGE_FLASHADR			80
#define VOLTWARN_FLASHADR			90

void Flash_Save_GyroOffset(long *Gyro_offset);
void Flash_Read_GyroOffset(long *Gyro_offset);
void Flash_Save_AccOffset(long *Accel_offset);
void Flash_Read_AccOffset(long *Accel_offset);
void Flash_Save_PID(void);
void Flash_Read_PID(void);
void Flash_Save_SafeAngle(void);
void Flash_Read_SafeAngle(void);
void Flash_Save_Language(void);
void Flash_Read_Language(void);
void Flash_Save_VoltWarn(void);
void Flash_Read_VoltWarn(void);
void Flash_Save_Delay(void);

#endif
