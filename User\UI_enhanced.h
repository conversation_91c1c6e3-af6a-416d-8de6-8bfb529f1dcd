#ifndef __UI_ENHANCED_H__
#define __UI_ENHANCED_H__

#include "sys.h"

/* ======================== 增强版UI函数声明 ======================== */

// 相机信息显示函数
void Display_Camera_Info(uint8_t x, uint8_t y);
void Display_Target_Coordinates(uint8_t x, uint8_t y);
void Display_Target_Angles(uint8_t x, uint8_t y);
void Display_Control_Mode(uint8_t x, uint8_t y);

// 增强版模式显示函数
void Balance_Mode_Enhanced(void);
void Standby_Mode_Enhanced(void);

// 新增调试模式
void Camera_Debug_Mode(void);

/* ======================== 显示字符串声明 ======================== */

extern unsigned char CameraUI_String[3][4][12];
extern unsigned char ControlMode_String[3][2][8];
extern unsigned char CameraStatus_String[3][3][8];

#endif /* __UI_ENHANCED_H__ */
