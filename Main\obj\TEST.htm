<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\obj\TEST.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\obj\TEST.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Sat Aug 02 11:55:28 2025
<BR><P>
<H3>Maximum Stack Usage =        312 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; AdjustAngle_Settings &rArr; run_self_test &rArr; mpu_run_self_test &rArr; get_st_biases &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">MemManage_Handler</a><BR>
 <LI><a href="#[4]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">BusFault_Handler</a><BR>
 <LI><a href="#[5]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">UsageFault_Handler</a><BR>
 <LI><a href="#[6]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">SVC_Handler</a><BR>
 <LI><a href="#[7]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">DebugMon_Handler</a><BR>
 <LI><a href="#[8]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">PendSV_Handler</a><BR>
 <LI><a href="#[9]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">SysTick_Handler</a><BR>
 <LI><a href="#[1c]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC1_2_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1c]">ADC1_2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[39]">ADC3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1f]">CAN1_RX1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[20]">CAN1_SCE_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[15]">DMA1_Channel1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[16]">DMA1_Channel2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[17]">DMA1_Channel3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[18]">DMA1_Channel4_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[19]">DMA1_Channel5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel6_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel7_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[42]">DMA2_Channel1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[43]">DMA2_Channel2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[44]">DMA2_Channel3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[45]">DMA2_Channel4_5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[32]">EXTI15_10_IRQHandler</a> from main.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[21]">EXTI9_5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[e]">FLASH_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3a]">FSMC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2a]">I2C1_ER_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[29]">I2C1_EV_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2c]">I2C2_ER_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2b]">I2C2_EV_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[b]">PVD_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[f]">RCC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[33]">RTCAlarm_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[d]">RTC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3b]">SDIO_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2d]">SPI1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2e]">SPI2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3d]">SPI3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[c]">TAMPER_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[22]">TIM1_BRK_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[25]">TIM1_CC_IRQHandler</a> from ultrasonic.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[24]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[23]">TIM1_UP_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[26]">TIM2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[27]">TIM3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[28]">TIM4_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3c]">TIM5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[40]">TIM6_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[41]">TIM7_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[35]">TIM8_BRK_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[38]">TIM8_CC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[37]">TIM8_TRG_COM_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[36]">TIM8_UP_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3e]">UART4_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3f]">UART5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2f]">USART1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[30]">USART2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[31]">USART3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[34]">USBWakeUp_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1d]">USB_HP_CAN1_TX_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1e]">USB_LP_CAN1_RX0_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[a]">WWDG_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[47]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f10x_hd.o(.text)
 <LI><a href="#[46]">main</a> from main.o(.text) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[47]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(.text)
</UL>
<P><STRONG><a name="[135]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[48]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[128]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[136]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[137]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[138]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[139]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[13a]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[13b]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[9e]"></a>WFI_SET</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, sys.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sys_Standby
</UL>

<P><STRONG><a name="[13c]"></a>INTX_DISABLE</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, sys.o(.emb_text), UNUSED)

<P><STRONG><a name="[13d]"></a>INTX_ENABLE</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, sys.o(.emb_text), UNUSED)

<P><STRONG><a name="[13e]"></a>MSR_MSP</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sys.o(.emb_text), UNUSED)

<P><STRONG><a name="[46]"></a>main</STRONG> (Thumb, 264 bytes, Stack size 0 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = main &rArr; AdjustAngle_Settings &rArr; run_self_test &rArr; mpu_run_self_test &rArr; get_st_biases &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_Init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stm32_Clock_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Standby_Mode
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Settings_Mode
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SafeAngle_Settings
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Init
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Other_Settings_Mode
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_AllGDRAM
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MYDMA_Config
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LanguageSettings_Mode
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JTAG_Set
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_VoltWarn
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_Language
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FactorySettings_Mode
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Balance_Mode
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdjustAngle_Settings
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[32]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 296 bytes, Stack size 16 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = EXTI15_10_IRQHandler &rArr; MPU6050_Pose &rArr; atan2 &rArr; atan &rArr; __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Motor
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_GDRAM
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Ring_Control
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Check
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BlueData_Receive_Anl
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>PID_Init</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, control.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = PID_Init &rArr; Flash_Save_SafeAngle &rArr; STMFLASH_Write &rArr; STMFLASH_Write_NoCheck &rArr; STMFLASH_WriteHalfWord &rArr; STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_SafeAngle
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_SafeAngle
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_PID
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[64]"></a>CONTROL</STRONG> (Thumb, 544 bytes, Stack size 32 bytes, control.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = CONTROL &rArr; number_to_dps &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;number_to_dps
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Motor
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[62]"></a>BlueData_Receive_Anl</STRONG> (Thumb, 182 bytes, Stack size 0 bytes, data_transfer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[76]"></a>Data_Send_Attitude</STRONG> (Thumb, 238 bytes, Stack size 24 bytes, data_transfer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_SendData
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Data
</UL>

<P><STRONG><a name="[78]"></a>Data_Send_PID1</STRONG> (Thumb, 374 bytes, Stack size 16 bytes, data_transfer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Data
</UL>

<P><STRONG><a name="[7a]"></a>Send_Data</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, data_transfer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Data_Send_PID1
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Data_Send_Attitude
</UL>

<P><STRONG><a name="[7b]"></a>Flash_Save_GyroOffset</STRONG> (Thumb, 104 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Flash_Save_GyroOffset &rArr; STMFLASH_Write &rArr; STMFLASH_Write_NoCheck &rArr; STMFLASH_WriteHalfWord &rArr; STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[7e]"></a>Flash_Read_GyroOffset</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Flash_Read_GyroOffset &rArr; STMFLASH_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[7f]"></a>Flash_Save_AccOffset</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Flash_Save_AccOffset &rArr; STMFLASH_Write &rArr; STMFLASH_Write_NoCheck &rArr; STMFLASH_WriteHalfWord &rArr; STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[80]"></a>Flash_Read_AccOffset</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Flash_Read_AccOffset &rArr; STMFLASH_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[81]"></a>Flash_Save_PID</STRONG> (Thumb, 216 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Flash_Save_PID &rArr; STMFLASH_Write &rArr; STMFLASH_Write_NoCheck &rArr; STMFLASH_WriteHalfWord &rArr; STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FactorySettings_Mode
</UL>

<P><STRONG><a name="[6c]"></a>Flash_Read_PID</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Flash_Read_PID &rArr; STMFLASH_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Init
</UL>

<P><STRONG><a name="[6e]"></a>Flash_Save_SafeAngle</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Flash_Save_SafeAngle &rArr; STMFLASH_Write &rArr; STMFLASH_Write_NoCheck &rArr; STMFLASH_WriteHalfWord &rArr; STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SafeAngle_Settings
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Init
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FactorySettings_Mode
</UL>

<P><STRONG><a name="[6d]"></a>Flash_Read_SafeAngle</STRONG> (Thumb, 54 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Flash_Read_SafeAngle &rArr; STMFLASH_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SafeAngle_Settings
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Init
</UL>

<P><STRONG><a name="[83]"></a>Flash_Save_Language</STRONG> (Thumb, 50 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Flash_Save_Language &rArr; STMFLASH_Write &rArr; STMFLASH_Write_NoCheck &rArr; STMFLASH_WriteHalfWord &rArr; STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LanguageSettings_Mode
</UL>

<P><STRONG><a name="[57]"></a>Flash_Read_Language</STRONG> (Thumb, 64 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Flash_Read_Language &rArr; STMFLASH_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[84]"></a>Flash_Save_VoltWarn</STRONG> (Thumb, 50 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Flash_Save_VoltWarn &rArr; STMFLASH_Write &rArr; STMFLASH_Write_NoCheck &rArr; STMFLASH_WriteHalfWord &rArr; STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Other_Settings_Mode
</UL>

<P><STRONG><a name="[58]"></a>Flash_Read_VoltWarn</STRONG> (Thumb, 64 bytes, Stack size 32 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Flash_Read_VoltWarn &rArr; STMFLASH_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Other_Settings_Mode
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[85]"></a>Flash_Save_Delay</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, data_transfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Flash_Save_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SafeAngle_Settings
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Other_Settings_Mode
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LanguageSettings_Mode
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FactorySettings_Mode
</UL>

<P><STRONG><a name="[59]"></a>Standby_Mode</STRONG> (Thumb, 560 bytes, Stack size 16 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = Standby_Mode &rArr; OLED_Display0612Num &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0612Num1dot
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0612Num
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0507Num1dot
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5a]"></a>Balance_Mode</STRONG> (Thumb, 578 bytes, Stack size 16 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = Balance_Mode &rArr; OLED_Display0612Num &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0612Num1dot
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0612Num
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0507Num1dot
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8b]"></a>Display_Menux</STRONG> (Thumb, 246 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = Display_Menux &rArr; OLED_PutChar5x7 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PutChar5x7
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Settings_Mode
</UL>

<P><STRONG><a name="[5b]"></a>Settings_Mode</STRONG> (Thumb, 838 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = Settings_Mode &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Menux
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8d]"></a>Display_SaveMasBox</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = Display_SaveMasBox &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Drawline_X
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SafeAngle_Settings
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Other_Settings_Mode
</UL>

<P><STRONG><a name="[8f]"></a>Display_PID_data</STRONG> (Thumb, 438 bytes, Stack size 16 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = Display_PID_data &rArr; OLED_Display0612Num &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0612Num
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
</UL>

<P><STRONG><a name="[5c]"></a>PID_Settings</STRONG> (Thumb, 1258 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = PID_Settings &rArr; Display_PID_data &rArr; OLED_Display0612Num &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_PID_data
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SaveMasBox
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_Delay
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_PID
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_PID
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[91]"></a>Display_SafeAngle_data</STRONG> (Thumb, 160 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = Display_SafeAngle_data &rArr; OLED_Display0612Num &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0612Num
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SafeAngle_Settings
</UL>

<P><STRONG><a name="[5d]"></a>SafeAngle_Settings</STRONG> (Thumb, 604 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = SafeAngle_Settings &rArr; Display_SafeAngle_data &rArr; OLED_Display0612Num &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SafeAngle_data
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SaveMasBox
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_Delay
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_SafeAngle
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_SafeAngle
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5e]"></a>AdjustAngle_Settings</STRONG> (Thumb, 538 bytes, Stack size 16 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = AdjustAngle_Settings &rArr; run_self_test &rArr; mpu_run_self_test &rArr; get_st_biases &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PutChar0612
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Drawline_X
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[95]"></a>Display_LanguageCursor</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = Display_LanguageCursor &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LanguageSettings_Mode
</UL>

<P><STRONG><a name="[5f]"></a>LanguageSettings_Mode</STRONG> (Thumb, 406 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = LanguageSettings_Mode &rArr; Display_LanguageCursor &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_LanguageCursor
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_Delay
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_Language
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[96]"></a>Display_FactoryBox</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = Display_FactoryBox &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Drawline_X
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FactorySettings_Mode
</UL>

<P><STRONG><a name="[60]"></a>FactorySettings_Mode</STRONG> (Thumb, 488 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = FactorySettings_Mode &rArr; Display_FactoryBox &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_FactoryBox
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_Delay
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_PID
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_SafeAngle
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[97]"></a>Display_OtherSettings_data</STRONG> (Thumb, 306 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = Display_OtherSettings_data &rArr; OLED_Display0612Num1dot &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0612Num1dot
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Other_Settings_Mode
</UL>

<P><STRONG><a name="[61]"></a>Other_Settings_Mode</STRONG> (Thumb, 528 bytes, Stack size 8 bytes, ui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = Other_Settings_Mode &rArr; Display_OtherSettings_data &rArr; OLED_Display0612Num1dot &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_OtherSettings_data
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SaveMasBox
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_Delay
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_VoltWarn
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_VoltWarn
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[98]"></a>VoltWarn_Mode</STRONG> (Thumb, 118 bytes, Stack size 8 bytes, ui.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Drawline_X
</UL>

<P><STRONG><a name="[4b]"></a>Delay_Init</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, delay.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[55]"></a>delay_ms</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, delay.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Write2
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Write
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_Delay_ms
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_Delay
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SafeAngle_Settings
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Other_Settings_Mode
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MYDMA_Config
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LanguageSettings_Mode
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FactorySettings_Mode
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdjustAngle_Settings
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a9]"></a>delay_us</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, delay.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_ReadByte2
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_SendByte2
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_NoAck2
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Ack2
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_WaitAck2
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Stop2
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Start2
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_WaitDone
</UL>

<P><STRONG><a name="[9c]"></a>MY_NVIC_SetVectorTable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sys.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MYRCC_DeInit
</UL>

<P><STRONG><a name="[9a]"></a>MY_NVIC_PriorityGroupConfig</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, sys.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_Init
</UL>

<P><STRONG><a name="[99]"></a>MY_NVIC_Init</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MY_NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_PriorityGroupConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_Init
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>

<P><STRONG><a name="[108]"></a>Ex_NVIC_Config</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Ex_NVIC_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>

<P><STRONG><a name="[9b]"></a>MYRCC_DeInit</STRONG> (Thumb, 90 bytes, Stack size 4 bytes, sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = MYRCC_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_SetVectorTable
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stm32_Clock_Init
</UL>

<P><STRONG><a name="[9d]"></a>Sys_Standby</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, sys.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WFI_SET
</UL>

<P><STRONG><a name="[13f]"></a>Sys_Soft_Reset</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sys.o(.text), UNUSED)

<P><STRONG><a name="[4c]"></a>JTAG_Set</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, sys.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4a]"></a>Stm32_Clock_Init</STRONG> (Thumb, 134 bytes, Stack size 12 bytes, sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Stm32_Clock_Init &rArr; MYRCC_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MYRCC_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[140]"></a>_sys_exit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usart.o(.text), UNUSED)

<P><STRONG><a name="[141]"></a>fputc</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, usart.o(.text), UNUSED)

<P><STRONG><a name="[a0]"></a>USART1_SendData</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, usart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_SendString
</UL>

<P><STRONG><a name="[9f]"></a>USART1_SendString</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_SendData
</UL>

<P><STRONG><a name="[51]"></a>USART1_Init</STRONG> (Thumb, 198 bytes, Stack size 40 bytes, usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = USART1_Init &rArr; __aeabi_frsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[77]"></a>USART3_SendData</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, usart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_SendString
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Data_Send_PID1
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Data_Send_Attitude
</UL>

<P><STRONG><a name="[a2]"></a>USART3_SendString</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_SendData
</UL>

<P><STRONG><a name="[53]"></a>USART3_Init</STRONG> (Thumb, 206 bytes, Stack size 40 bytes, usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = USART3_Init &rArr; __aeabi_frsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a3]"></a>TIM1_PWM_Init</STRONG> (Thumb, 236 bytes, Stack size 0 bytes, motor.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
</UL>

<P><STRONG><a name="[65]"></a>Set_Motor</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, motor.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[4d]"></a>Motor_Init</STRONG> (Thumb, 72 bytes, Stack size 4 bytes, motor.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Motor_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_PWM_Init
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Motor
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4e]"></a>LED_Init</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, led.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6a]"></a>Key_Check</STRONG> (Thumb, 380 bytes, Stack size 0 bytes, led.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[a6]"></a>Led_Flash3</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, led.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Ring_Control
</UL>

<P><STRONG><a name="[a5]"></a>Led_Flash2</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, led.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Ring_Control
</UL>

<P><STRONG><a name="[a4]"></a>Led_Flash1</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, led.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Ring_Control
</UL>

<P><STRONG><a name="[69]"></a>LED_Ring_Control</STRONG> (Thumb, 66 bytes, Stack size 4 bytes, led.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LED_Ring_Control
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Led_Flash1
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Led_Flash2
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Led_Flash3
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[ae]"></a>STMFLASH_Unlock</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stmflash.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>

<P><STRONG><a name="[af]"></a>STMFLASH_Lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stmflash.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>

<P><STRONG><a name="[a8]"></a>STMFLASH_GetStatus</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stmflash.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_WaitDone
</UL>

<P><STRONG><a name="[a7]"></a>STMFLASH_WaitDone</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, stmflash.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_GetStatus
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_WriteHalfWord
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_ErasePage
</UL>

<P><STRONG><a name="[aa]"></a>STMFLASH_ErasePage</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stmflash.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = STMFLASH_ErasePage &rArr; STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_WaitDone
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>

<P><STRONG><a name="[ab]"></a>STMFLASH_WriteHalfWord</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stmflash.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = STMFLASH_WriteHalfWord &rArr; STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_WaitDone
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write_NoCheck
</UL>

<P><STRONG><a name="[ad]"></a>STMFLASH_ReadHalfWord</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stmflash.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>

<P><STRONG><a name="[ac]"></a>STMFLASH_Write_NoCheck</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, stmflash.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = STMFLASH_Write_NoCheck &rArr; STMFLASH_WriteHalfWord &rArr; STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_WriteHalfWord
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>

<P><STRONG><a name="[7c]"></a>STMFLASH_Read</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, stmflash.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = STMFLASH_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_ReadHalfWord
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_VoltWarn
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_Language
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_PID
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_AccOffset
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_AccOffset
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_GyroOffset
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_GyroOffset
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_SafeAngle
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_SafeAngle
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_PID
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_VoltWarn
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_Language
</UL>

<P><STRONG><a name="[7d]"></a>STMFLASH_Write</STRONG> (Thumb, 224 bytes, Stack size 40 bytes, stmflash.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = STMFLASH_Write &rArr; STMFLASH_Write_NoCheck &rArr; STMFLASH_WriteHalfWord &rArr; STMFLASH_WaitDone
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write_NoCheck
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_ErasePage
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Lock
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Unlock
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_VoltWarn
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_Language
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_PID
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_AccOffset
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_GyroOffset
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_SafeAngle
</UL>

<P><STRONG><a name="[b1]"></a>OLED_I2C_Stop</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, oled.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteDat
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCmd
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Wait_Ack
</UL>

<P><STRONG><a name="[b0]"></a>OLED_I2C_Wait_Ack</STRONG> (Thumb, 76 bytes, Stack size 4 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = OLED_I2C_Wait_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Send_Byte
</UL>

<P><STRONG><a name="[b2]"></a>OLED_I2C_Send_Byte</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = OLED_I2C_Send_Byte &rArr; OLED_I2C_Wait_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Wait_Ack
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteDat
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCmd
</UL>

<P><STRONG><a name="[b4]"></a>OLED_I2C_Start</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, oled.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteDat
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCmd
</UL>

<P><STRONG><a name="[b3]"></a>OLED_WriteCmd</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = OLED_WriteCmd &rArr; OLED_I2C_Send_Byte &rArr; OLED_I2C_Wait_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Send_Byte
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Fill
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[b5]"></a>OLED_Set_Pos</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = OLED_Set_Pos &rArr; OLED_WriteCmd &rArr; OLED_I2C_Send_Byte &rArr; OLED_I2C_Wait_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteStr
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_GDRAM
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[b6]"></a>OLED_WriteDat</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = OLED_WriteDat &rArr; OLED_I2C_Send_Byte &rArr; OLED_I2C_Wait_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Send_Byte
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteStr
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Fill
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_GDRAM
</UL>

<P><STRONG><a name="[b7]"></a>OLED_Fill</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = OLED_Fill &rArr; OLED_WriteDat &rArr; OLED_I2C_Send_Byte &rArr; OLED_I2C_Wait_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteDat
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[4f]"></a>OLED_Init</STRONG> (Thumb, 246 bytes, Stack size 8 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = OLED_Init &rArr; OLED_Fill &rArr; OLED_WriteDat &rArr; OLED_I2C_Send_Byte &rArr; OLED_I2C_Wait_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Fill
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCmd
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6b]"></a>OLED_Refresh_GDRAM</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = OLED_Refresh_GDRAM &rArr; OLED_Set_Pos &rArr; OLED_WriteCmd &rArr; OLED_I2C_Send_Byte &rArr; OLED_I2C_Wait_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteDat
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_AllGDRAM
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[50]"></a>OLED_Refresh_AllGDRAM</STRONG> (Thumb, 52 bytes, Stack size 4 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OLED_Refresh_AllGDRAM &rArr; OLED_Refresh_GDRAM &rArr; OLED_Set_Pos &rArr; OLED_WriteCmd &rArr; OLED_I2C_Send_Byte &rArr; OLED_I2C_Wait_Ack
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_GDRAM
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b9]"></a>OLED_Draw_dots</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_Draw_dots
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_circle
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_lingxing
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Drawline
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Drawline_Y
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1414
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PutChar
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PutChar5x7
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PutChar0612
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Drawline_X
</UL>

<P><STRONG><a name="[b8]"></a>OLED_PutChar</STRONG> (Thumb, 206 bytes, Stack size 56 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_dots
</UL>

<P><STRONG><a name="[94]"></a>OLED_PutChar0612</STRONG> (Thumb, 154 bytes, Stack size 56 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_dots
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdjustAngle_Settings
</UL>

<P><STRONG><a name="[8c]"></a>OLED_PutChar5x7</STRONG> (Thumb, 136 bytes, Stack size 44 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = OLED_PutChar5x7 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_dots
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteENstr5x7
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1414
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Menux
</UL>

<P><STRONG><a name="[ba]"></a>OLED_WriteChstr1414</STRONG> (Thumb, 330 bytes, Stack size 68 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_dots
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PutChar5x7
</UL>

<P><STRONG><a name="[87]"></a>OLED_WriteChstr1212</STRONG> (Thumb, 346 bytes, Stack size 68 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_dots
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PutChar0612
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0612Num1dot
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0612Num
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VoltWarn_Mode
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_OtherSettings_data
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_FactoryBox
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_LanguageCursor
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SafeAngle_data
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_PID_data
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SaveMasBox
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Standby_Mode
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Settings_Mode
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SafeAngle_Settings
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Other_Settings_Mode
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LanguageSettings_Mode
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FactorySettings_Mode
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Balance_Mode
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdjustAngle_Settings
</UL>

<P><STRONG><a name="[bb]"></a>OLED_WriteENstr5x7</STRONG> (Thumb, 38 bytes, Stack size 20 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = OLED_WriteENstr5x7 &rArr; OLED_PutChar5x7 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PutChar5x7
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display0507Num1dot
</UL>

<P><STRONG><a name="[8e]"></a>OLED_Drawline_X</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = OLED_Drawline_X &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_dots
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Drawline
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VoltWarn_Mode
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_FactoryBox
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SaveMasBox
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdjustAngle_Settings
</UL>

<P><STRONG><a name="[bc]"></a>OLED_Drawline_Y</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_dots
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Drawline
</UL>

<P><STRONG><a name="[bd]"></a>OLED_Drawline</STRONG> (Thumb, 264 bytes, Stack size 56 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Drawline_Y
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_dots
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Drawline_X
</UL>

<P><STRONG><a name="[be]"></a>OLED_Draw_lingxing</STRONG> (Thumb, 230 bytes, Stack size 28 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_dots
</UL>

<P><STRONG><a name="[bf]"></a>OLED_Draw_circle</STRONG> (Thumb, 230 bytes, Stack size 28 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Draw_dots
</UL>

<P><STRONG><a name="[86]"></a>OLED_Clear</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, oled.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Standby_Mode
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Settings_Mode
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SafeAngle_Settings
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Other_Settings_Mode
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LanguageSettings_Mode
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FactorySettings_Mode
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Balance_Mode
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdjustAngle_Settings
</UL>

<P><STRONG><a name="[c0]"></a>OLED_WriteStr</STRONG> (Thumb, 216 bytes, Stack size 32 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteDat
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
</UL>

<P><STRONG><a name="[89]"></a>OLED_Display0612Num</STRONG> (Thumb, 204 bytes, Stack size 36 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = OLED_Display0612Num &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SafeAngle_data
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_PID_data
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Standby_Mode
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Balance_Mode
</UL>

<P><STRONG><a name="[88]"></a>OLED_Display0612Num1dot</STRONG> (Thumb, 208 bytes, Stack size 32 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = OLED_Display0612Num1dot &rArr; OLED_WriteChstr1212 &rArr; OLED_PutChar0612 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteChstr1212
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_OtherSettings_data
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Standby_Mode
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Balance_Mode
</UL>

<P><STRONG><a name="[8a]"></a>OLED_Display0507Num1dot</STRONG> (Thumb, 202 bytes, Stack size 28 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = OLED_Display0507Num1dot &rArr; OLED_WriteENstr5x7 &rArr; OLED_PutChar5x7 &rArr; OLED_Draw_dots
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteENstr5x7
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Standby_Mode
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Balance_Mode
</UL>

<P><STRONG><a name="[52]"></a>MYDMA_Config</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, dma.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MYDMA_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c1]"></a>TIM3_Init</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, ultrasonic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ultrasonic_init
</UL>

<P><STRONG><a name="[c2]"></a>ultrasonic_init</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, ultrasonic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_Init
</UL>

<P><STRONG><a name="[25]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 362 bytes, Stack size 0 bytes, ultrasonic.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>ADC3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Channel4_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>USB_HP_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>USB_LP_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[c3]"></a>AnBT_DMP_I2C_Write</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_WaitAck
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Stop
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Start
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_DEV_CFG
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_gyro_fsr
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_accel_fsr
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_int_latched
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_int_enable
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_mem
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
</UL>

<P><STRONG><a name="[c8]"></a>AnBT_DMP_I2C_Read</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = AnBT_DMP_I2C_Read &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_WaitAck
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Stop
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Start
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_SendByte
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_ReadByte
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_NoAck
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Ack
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_DEV_CFG
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_accel_prod_shift
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_mem
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_fifo_stream
</UL>

<P><STRONG><a name="[cc]"></a>dmp_load_motion_driver_firmware</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = dmp_load_motion_driver_firmware &rArr; mpu_load_firmware &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>

<P><STRONG><a name="[ce]"></a>dmp_set_orientation</STRONG> (Thumb, 290 bytes, Stack size 32 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = dmp_set_orientation &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>

<P><STRONG><a name="[d0]"></a>dmp_set_gyro_bias</STRONG> (Thumb, 294 bytes, Stack size 24 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = dmp_set_gyro_bias &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[d1]"></a>dmp_set_accel_bias</STRONG> (Thumb, 300 bytes, Stack size 48 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = dmp_set_accel_bias &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_sens
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[d3]"></a>dmp_set_fifo_rate</STRONG> (Thumb, 138 bytes, Stack size 32 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = dmp_set_fifo_rate &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>

<P><STRONG><a name="[142]"></a>dmp_get_fifo_rate</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, anbt_dmp_driver.o(.text), UNUSED)

<P><STRONG><a name="[d4]"></a>dmp_set_tap_thresh</STRONG> (Thumb, 396 bytes, Stack size 40 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = dmp_set_tap_thresh &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_fsr
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[d6]"></a>dmp_set_tap_axes</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = dmp_set_tap_axes &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[d7]"></a>dmp_set_tap_count</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = dmp_set_tap_count &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[d8]"></a>dmp_set_tap_time</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = dmp_set_tap_time &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[d9]"></a>dmp_set_tap_time_multi</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = dmp_set_tap_time_multi &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[da]"></a>dmp_set_shake_reject_thresh</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = dmp_set_shake_reject_thresh &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[db]"></a>dmp_set_shake_reject_time</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = dmp_set_shake_reject_time &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[dc]"></a>dmp_set_shake_reject_timeout</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = dmp_set_shake_reject_timeout &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[dd]"></a>dmp_get_pedometer_step_count</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_mem
</UL>

<P><STRONG><a name="[df]"></a>dmp_set_pedometer_step_count</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>

<P><STRONG><a name="[e0]"></a>dmp_get_pedometer_walk_time</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_mem
</UL>

<P><STRONG><a name="[e1]"></a>dmp_set_pedometer_walk_time</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>

<P><STRONG><a name="[e2]"></a>dmp_enable_6x_lp_quat</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = dmp_enable_6x_lp_quat &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[e5]"></a>dmp_enable_lp_quat</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = dmp_enable_lp_quat &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[e6]"></a>dmp_enable_gyro_cal</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = dmp_enable_gyro_cal &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[e7]"></a>dmp_enable_feature</STRONG> (Thumb, 530 bytes, Stack size 24 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = dmp_enable_feature &rArr; dmp_set_tap_thresh &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_gyro_cal
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_timeout
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_time
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_thresh
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time_multi
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_count
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_axes
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>

<P><STRONG><a name="[143]"></a>dmp_get_enabled_features</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, anbt_dmp_driver.o(.text), UNUSED)

<P><STRONG><a name="[e8]"></a>dmp_set_interrupt_mode</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, anbt_dmp_driver.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>

<P><STRONG><a name="[e9]"></a>dmp_read_fifo</STRONG> (Thumb, 508 bytes, Stack size 88 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_fifo_stream
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;decode_gesture
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
</UL>

<P><STRONG><a name="[144]"></a>dmp_register_tap_cb</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, anbt_dmp_driver.o(.text), UNUSED)

<P><STRONG><a name="[145]"></a>dmp_register_android_orient_cb</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, anbt_dmp_driver.o(.text), UNUSED)

<P><STRONG><a name="[ec]"></a>AnBT_DMP_Delay_ms</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AnBT_DMP_Delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_DEV_CFG
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
</UL>

<P><STRONG><a name="[e4]"></a>mpu_reset_fifo</STRONG> (Thumb, 450 bytes, Stack size 8 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = mpu_reset_fifo &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_Delay_ms
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_fifo_stream
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
</UL>

<P><STRONG><a name="[ee]"></a>mpu_set_lpf</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = mpu_set_lpf &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_DEV_CFG
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
</UL>

<P><STRONG><a name="[ef]"></a>mpu_configure_fifo</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = mpu_configure_fifo &rArr; set_int_enable &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_int_enable
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_DEV_CFG
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>

<P><STRONG><a name="[f0]"></a>mpu_set_int_latched</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = mpu_set_int_latched &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
</UL>

<P><STRONG><a name="[f1]"></a>mpu_lp_accel_mode</STRONG> (Thumb, 224 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_int_latched
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
</UL>

<P><STRONG><a name="[f2]"></a>mpu_set_sample_rate</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_DEV_CFG
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>

<P><STRONG><a name="[f3]"></a>mpu_set_bypass</STRONG> (Thumb, 328 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = mpu_set_bypass &rArr; AnBT_DMP_I2C_Read &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_Delay_ms
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_DEV_CFG
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
</UL>

<P><STRONG><a name="[92]"></a>mpu_set_dmp_state</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = mpu_set_dmp_state &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_int_enable
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdjustAngle_Settings
</UL>

<P><STRONG><a name="[d2]"></a>mpu_get_accel_sens</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_accel_bias
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[103]"></a>mpu_get_gyro_sens</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[f4]"></a>mpu_set_sensors</STRONG> (Thumb, 224 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = mpu_set_sensors &rArr; mpu_set_int_latched &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_int_latched
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_Delay_ms
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_DEV_CFG
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>

<P><STRONG><a name="[f5]"></a>mpu_set_accel_fsr</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = mpu_set_accel_fsr &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_DEV_CFG
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[f6]"></a>mpu_set_gyro_fsr</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = mpu_set_gyro_fsr &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_DEV_CFG
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[102]"></a>mpu_get_fifo_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[101]"></a>mpu_get_sample_rate</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[100]"></a>mpu_get_lpf</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[d5]"></a>mpu_get_accel_fsr</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
</UL>

<P><STRONG><a name="[ff]"></a>mpu_get_gyro_fsr</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[fe]"></a>mpu_run_self_test</STRONG> (Thumb, 278 bytes, Stack size 88 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = mpu_run_self_test &rArr; get_st_biases &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_gyro_fsr
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_lpf
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_sample_rate
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_fifo_config
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_gyro_fsr
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_accel_fsr
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_fsr
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[93]"></a>run_self_test</STRONG> (Thumb, 186 bytes, Stack size 56 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = run_self_test &rArr; mpu_run_self_test &rArr; get_st_biases &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_gyro_sens
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_sens
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_accel_bias
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_gyro_bias
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_AccOffset
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_AccOffset
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_GyroOffset
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_GyroOffset
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdjustAngle_Settings
</UL>

<P><STRONG><a name="[106]"></a>AnBT_DMP_MPU6050_DEV_CFG</STRONG> (Thumb, 414 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = AnBT_DMP_MPU6050_DEV_CFG &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_gyro_fsr
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_accel_fsr
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_Delay_ms
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>

<P><STRONG><a name="[56]"></a>AnBT_DMP_MPU6050_Init</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = AnBT_DMP_MPU6050_Init &rArr; run_self_test &rArr; mpu_run_self_test &rArr; get_st_biases &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Init_IO
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_DEV_CFG
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inv_orientation_matrix_to_scalar
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_fifo_rate
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_orientation
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_load_motion_driver_firmware
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ex_NVIC_Config
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_Init
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[63]"></a>MPU6050_Pose</STRONG> (Thumb, 572 bytes, Stack size 72 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = MPU6050_Pose &rArr; atan2 &rArr; atan &rArr; __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[cf]"></a>mpu_write_mem</STRONG> (Thumb, 196 bytes, Stack size 24 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_interrupt_mode
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_gyro_cal
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_pedometer_walk_time
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_pedometer_step_count
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_timeout
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_time
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_thresh
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time_multi
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_count
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_axes
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_fifo_rate
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_accel_bias
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_gyro_bias
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_orientation
</UL>

<P><STRONG><a name="[de]"></a>mpu_read_mem</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = mpu_read_mem &rArr; AnBT_DMP_I2C_Read &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_get_pedometer_walk_time
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_get_pedometer_step_count
</UL>

<P><STRONG><a name="[ea]"></a>mpu_read_fifo_stream</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
</UL>

<P><STRONG><a name="[cd]"></a>mpu_load_firmware</STRONG> (Thumb, 180 bytes, Stack size 48 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = mpu_load_firmware &rArr; mpu_write_mem &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_mem
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_load_motion_driver_firmware
</UL>

<P><STRONG><a name="[72]"></a>number_to_dps</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = number_to_dps &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
</UL>

<P><STRONG><a name="[107]"></a>MPU_I2C_Init_IO</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, mpu_i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>

<P><STRONG><a name="[c4]"></a>MPU_I2C_Start</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, mpu_i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Multiple_read
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Multiple_write
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Read
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Write
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>

<P><STRONG><a name="[c7]"></a>MPU_I2C_Stop</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, mpu_i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Multiple_read
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Multiple_write
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Read
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Write
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_WaitAck
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>

<P><STRONG><a name="[c6]"></a>MPU_I2C_WaitAck</STRONG> (Thumb, 88 bytes, Stack size 4 bytes, mpu_i2c.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Multiple_read
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Multiple_write
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Read
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Write
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>

<P><STRONG><a name="[cb]"></a>MPU_I2C_Ack</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, mpu_i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Multiple_read
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
</UL>

<P><STRONG><a name="[ca]"></a>MPU_I2C_NoAck</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, mpu_i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Multiple_read
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Read
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
</UL>

<P><STRONG><a name="[c5]"></a>MPU_I2C_SendByte</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, mpu_i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Multiple_read
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Multiple_write
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Read
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Write
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>

<P><STRONG><a name="[c9]"></a>MPU_I2C_ReadByte</STRONG> (Thumb, 114 bytes, Stack size 0 bytes, mpu_i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Multiple_read
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Read
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
</UL>

<P><STRONG><a name="[10e]"></a>MPU_I2C_Start2</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Write2
</UL>

<P><STRONG><a name="[10f]"></a>MPU_I2C_Stop2</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Write2
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_WaitAck2
</UL>

<P><STRONG><a name="[110]"></a>MPU_I2C_WaitAck2</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Stop2
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Write2
</UL>

<P><STRONG><a name="[111]"></a>MPU_I2C_Ack2</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>

<P><STRONG><a name="[112]"></a>MPU_I2C_NoAck2</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>

<P><STRONG><a name="[113]"></a>MPU_I2C_SendByte2</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_Write2
</UL>

<P><STRONG><a name="[114]"></a>MPU_I2C_ReadByte2</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>

<P><STRONG><a name="[115]"></a>Single_Write</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_WaitAck
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Stop
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Start
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_SendByte
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[116]"></a>Single_Write2</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_SendByte2
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_WaitAck2
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Stop2
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Start2
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[117]"></a>Single_Read</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_WaitAck
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Stop
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Start
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_SendByte
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_ReadByte
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_NoAck
</UL>

<P><STRONG><a name="[118]"></a>Multiple_write</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_WaitAck
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Stop
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Start
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_SendByte
</UL>

<P><STRONG><a name="[119]"></a>Multiple_read</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, mpu_i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_WaitAck
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Stop
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Start
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_SendByte
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_ReadByte
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_NoAck
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_I2C_Ack
</UL>

<P><STRONG><a name="[fd]"></a>__aeabi_ldivmod</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, ldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
</UL>

<P><STRONG><a name="[e3]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[146]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[147]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[11b]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[148]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[149]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[11c]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[10d]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
</UL>

<P><STRONG><a name="[71]"></a>__aeabi_fadd</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
</UL>

<P><STRONG><a name="[74]"></a>__aeabi_fsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
</UL>

<P><STRONG><a name="[6f]"></a>__aeabi_frsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_frsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_Init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
</UL>

<P><STRONG><a name="[70]"></a>__aeabi_fmul</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, fmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_accel_prod_shift
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Data_Send_Attitude
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;number_to_dps
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_Init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Standby_Mode
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Balance_Mode
</UL>

<P><STRONG><a name="[a1]"></a>__aeabi_fdiv</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, fdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_Init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
</UL>

<P><STRONG><a name="[10a]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[73]"></a>__aeabi_i2f</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_i2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;number_to_dps
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
</UL>

<P><STRONG><a name="[82]"></a>__aeabi_ui2f</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ffltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_ui2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Read_PID
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_Init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[75]"></a>__aeabi_f2iz</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, ffixi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_PID_data
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Save_PID
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Data_Send_Attitude
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Standby_Mode
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Balance_Mode
</UL>

<P><STRONG><a name="[79]"></a>__aeabi_f2uiz</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, ffixui.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Data_Send_PID1
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_Init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[66]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[134]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
</UL>

<P><STRONG><a name="[67]"></a>__aeabi_cdcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[68]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CONTROL
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[10b]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
</UL>

<P><STRONG><a name="[fb]"></a>__aeabi_cfcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cfcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>

<P><STRONG><a name="[f8]"></a>__aeabi_cfcmple</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, cfcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
</UL>

<P><STRONG><a name="[90]"></a>__aeabi_cfrcmple</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, cfrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Settings
</UL>

<P><STRONG><a name="[11a]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>

<P><STRONG><a name="[14a]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[11e]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>

<P><STRONG><a name="[11d]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = _float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>

<P><STRONG><a name="[122]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[11f]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>

<P><STRONG><a name="[123]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[125]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[126]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[127]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[12c]"></a>__ARM_scalbn</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, dscalb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[14b]"></a>scalbn</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, dscalb.o(.text), UNUSED)

<P><STRONG><a name="[49]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[14c]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[121]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[14d]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[120]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[14e]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[124]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[14f]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[129]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[150]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dclz77c.o(.text), UNUSED)

<P><STRONG><a name="[151]"></a>__decompress2</STRONG> (Thumb, 94 bytes, Stack size unknown bytes, __dclz77c.o(.text), UNUSED)

<P><STRONG><a name="[131]"></a>__ARM_fpclassify</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[12a]"></a>__kernel_poly</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[12b]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __mathlib_dbl_infnan &rArr; __ARM_scalbn
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[12d]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
</UL>

<P><STRONG><a name="[12e]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>

<P><STRONG><a name="[12f]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __mathlib_dbl_underflow &rArr; __ARM_scalbn
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[152]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[153]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[154]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[130]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[109]"></a>asin</STRONG> (Thumb, 572 bytes, Stack size 56 bytes, asin.o(i.asin))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = asin &rArr; __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
</UL>

<P><STRONG><a name="[133]"></a>atan</STRONG> (Thumb, 474 bytes, Stack size 40 bytes, atan.o(i.atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = atan &rArr; __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
</UL>

<P><STRONG><a name="[10c]"></a>atan2</STRONG> (Thumb, 374 bytes, Stack size 32 bytes, atan2.o(i.atan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = atan2 &rArr; atan &rArr; __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Pose
</UL>

<P><STRONG><a name="[132]"></a>sqrt</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[eb]"></a>decode_gesture</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, anbt_dmp_driver.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = decode_gesture
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
</UL>

<P><STRONG><a name="[ed]"></a>set_int_enable</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = set_int_enable &rArr; AnBT_DMP_I2C_Write &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
</UL>

<P><STRONG><a name="[f7]"></a>gyro_self_test</STRONG> (Thumb, 282 bytes, Stack size 56 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = gyro_self_test &rArr; AnBT_DMP_I2C_Read &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[f9]"></a>get_accel_prod_shift</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = get_accel_prod_shift &rArr; AnBT_DMP_I2C_Read &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>

<P><STRONG><a name="[fa]"></a>accel_self_test</STRONG> (Thumb, 186 bytes, Stack size 56 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = accel_self_test &rArr; get_accel_prod_shift &rArr; AnBT_DMP_I2C_Read &rArr; MPU_I2C_WaitAck
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmpeq
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_accel_prod_shift
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[fc]"></a>get_st_biases</STRONG> (Thumb, 1158 bytes, Stack size 88 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = get_st_biases &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_Delay_ms
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Read
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_I2C_Write
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[105]"></a>inv_row_2_scale</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inv_orientation_matrix_to_scalar
</UL>

<P><STRONG><a name="[104]"></a>inv_orientation_matrix_to_scalar</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, anbt_dmp_mpu6050.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = inv_orientation_matrix_to_scalar
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inv_row_2_scale
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnBT_DMP_MPU6050_Init
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
