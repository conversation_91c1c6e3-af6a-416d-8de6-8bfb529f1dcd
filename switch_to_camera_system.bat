@echo off
echo ========================================
echo    相机云台系统工程切换脚本
echo ========================================
echo.

:: 检查是否在正确的目录
if not exist "Main\Main.c" (
    echo 错误：请在工程根目录运行此脚本！
    pause
    exit /b 1
)

echo 第一步：备份原有文件...
if not exist "backup_original" mkdir backup_original

:: 备份原有文件
if exist "User\data_transfer.c" copy "User\data_transfer.c" "backup_original\" >nul
if exist "User\data_transfer.h" copy "User\data_transfer.h" "backup_original\" >nul
if exist "User\control.h" copy "User\control.h" "backup_original\" >nul
if exist "Main\Main.c" copy "Main\Main.c" "backup_original\" >nul
if exist "User\UI.c" copy "User\UI.c" "backup_original\" >nul

echo ✓ 原有文件已备份到 backup_original 目录

echo.
echo 第二步：替换核心文件...

:: 替换主程序文件
if exist "Main\Main_modified.c" (
    copy "Main\Main_modified.c" "Main\Main.c" >nul
    echo ✓ 已替换 Main.c
) else (
    echo ✗ 未找到 Main_modified.c
)

:: 替换数据传输文件
if exist "User\data_transfer_modified.c" (
    copy "User\data_transfer_modified.c" "User\data_transfer.c" >nul
    echo ✓ 已替换 data_transfer.c
) else (
    echo ✗ 未找到 data_transfer_modified.c
)

if exist "User\data_transfer_modified.h" (
    copy "User\data_transfer_modified.h" "User\data_transfer.h" >nul
    echo ✓ 已替换 data_transfer.h
) else (
    echo ✗ 未找到 data_transfer_modified.h
)

:: 替换控制头文件
if exist "User\control_modified.h" (
    copy "User\control_modified.h" "User\control.h" >nul
    echo ✓ 已替换 control.h
) else (
    echo ✗ 未找到 control_modified.h
)

echo.
echo 第三步：添加新文件...

:: 检查并复制相机控制文件
if exist "User\camera_control.c" (
    echo ✓ camera_control.c 已存在
) else (
    echo ✗ 未找到 camera_control.c，请确保文件存在
)

if exist "User\camera_control.h" (
    echo ✓ camera_control.h 已存在
) else (
    echo ✗ 未找到 camera_control.h，请确保文件存在
)

:: 检查增强版UI文件
if exist "User\UI_enhanced.c" (
    echo ✓ UI_enhanced.c 已存在
) else (
    echo ✗ 未找到 UI_enhanced.c，请确保文件存在
)

if exist "User\UI_enhanced.h" (
    echo ✓ UI_enhanced.h 已存在
) else (
    echo ✗ 未找到 UI_enhanced.h，请确保文件存在
)

echo.
echo 第四步：检查文档文件...

if exist "docs\Camera_Gimbal_Integration_Guide.md" (
    echo ✓ 集成指南文档已存在
) else (
    echo ✗ 未找到集成指南文档
)

if exist "docs\Deployment_Guide.md" (
    echo ✓ 部署指南文档已存在
) else (
    echo ✗ 未找到部署指南文档
)

if exist "docs\OLED_Display_Guide.md" (
    echo ✓ 显示指南文档已存在
) else (
    echo ✗ 未找到显示指南文档
)

if exist "README_Camera_Integration.md" (
    echo ✓ 项目说明文档已存在
) else (
    echo ✗ 未找到项目说明文档
)

echo.
echo ========================================
echo 切换完成！接下来需要手动操作：
echo ========================================
echo.
echo 1. 打开 Keil 项目 (Main\TEST.uvprojx)
echo.
echo 2. 在 USER 组中添加以下文件：
echo    - User\camera_control.c
echo    - User\UI_enhanced.c
echo.
echo 3. 检查包含路径设置：
echo    - 右键项目 → Options for Target
echo    - C/C++ → Include Paths
echo    - 确保包含 .\User 路径
echo.
echo 4. 编译项目：
echo    - 按 F7 或点击编译按钮
echo    - 检查是否有编译错误
echo.
echo 5. 硬件连接：
echo    - 串口3: PB10(TX), PB11(RX)
echo    - 波特率: 9600
echo    - 数据格式: 8N1
echo.
echo 6. 参数调整（如需要）：
echo    - 编辑 User\camera_control.h
echo    - 调整 ImageToActual, Distance 等参数
echo.
echo 7. 测试验证：
echo    - 发送测试数据包验证通信
echo    - 检查OLED显示是否正常
echo    - 验证控制功能
echo.
echo ========================================
echo 如有问题，请查看以下文档：
echo - docs\Deployment_Guide.md (部署指南)
echo - docs\OLED_Display_Guide.md (显示说明)
echo - README_Camera_Integration.md (项目说明)
echo ========================================
echo.
pause
