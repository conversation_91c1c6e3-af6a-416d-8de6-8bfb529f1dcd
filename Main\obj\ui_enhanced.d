.\obj\ui_enhanced.o: ..\User\UI_enhanced.c
.\obj\ui_enhanced.o: ..\SYSTEM\sys\sys.h
.\obj\ui_enhanced.o: D:\keil5\Keil\STM32F1xx_DFP\2.3.0\Device\Include\stm32f10x.h
.\obj\ui_enhanced.o: ..\SYSTEM\core_cm3.h
.\obj\ui_enhanced.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdint.h
.\obj\ui_enhanced.o: D:\keil5\Keil\STM32F1xx_DFP\2.3.0\Device\Include\system_stm32f10x.h
.\obj\ui_enhanced.o: ..\SYSTEM\delay\delay.h
.\obj\ui_enhanced.o: ..\SYSTEM\usart\usart.h
.\obj\ui_enhanced.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdio.h
.\obj\ui_enhanced.o: ..\DMP\anbt_dmp_mpu6050.h
.\obj\ui_enhanced.o: D:\keil5\ARM\ARMCC\Bin\..\include\string.h
.\obj\ui_enhanced.o: ..\DMP\anbt_dmp_driver.h
.\obj\ui_enhanced.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdlib.h
.\obj\ui_enhanced.o: D:\keil5\ARM\ARMCC\Bin\..\include\math.h
.\obj\ui_enhanced.o: ..\DMP\anbt_dmp_fun.h
.\obj\ui_enhanced.o: ..\Hardware\encoder.h
.\obj\ui_enhanced.o: ..\User\control.h
.\obj\ui_enhanced.o: ..\Hardware\motor.h
.\obj\ui_enhanced.o: ..\Hardware\led.h
.\obj\ui_enhanced.o: ..\Hardware\DMA.h
.\obj\ui_enhanced.o: ..\User\data_transfer.h
.\obj\ui_enhanced.o: ..\User\camera_control.h
.\obj\ui_enhanced.o: ..\Hardware\stmflash.h
.\obj\ui_enhanced.o: ..\SYSTEM\flash\flash.h
.\obj\ui_enhanced.o: ..\Hardware\OLED\oled.h
.\obj\ui_enhanced.o: ..\Hardware\ADC\ADC.h
.\obj\ui_enhanced.o: ..\User\UI.h
