/*
 * Bitmap.h
 *
 *  Created on: 2012-2-5
 *      Author: Administrator
 */

#ifndef BITMAP_H_
#define BITMAP_H_

static const unsigned char BMP_FreeLink[8][64]={
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xC0,0xE0,0xE0,0xE0,0xC0,0x00,0x00,0x00,0x00,0x80,0xF8,0xFC,
0xFC,0xFC,0xF8,0x80,0x00,0x00,0x00,0x00,0xC0,0xE0,0xE0,0xE0,0xC0,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0xF0,0xF0,0xF0,
0xE0,0xE0,0xC0,0xE0,0xF1,0xFF,0xFF,0x7F,0x3F,0x1F,0x1F,0x0F,0x0F,0x0F,0x8F,0xEF,
0xFF,0xCF,0x0F,0x0F,0x0F,0x0F,0x1F,0x1F,0x3F,0x7F,0xFF,0xFF,0xF1,0xE0,0xC0,0xE0,
0xE0,0xF0,0xF0,0xF0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xC0,0xC0,0xC1,0xC3,0xF3,
0xFF,0xFF,0xFF,0x1F,0x07,0x01,0x00,0x00,0x00,0xC0,0xE0,0x78,0x7C,0x67,0x63,0x61,
0x60,0x61,0x67,0x6E,0x7C,0x70,0x60,0xC0,0x80,0x00,0x00,0x01,0x07,0x1F,0xFF,0xFF,
0xFF,0xF3,0xC3,0xC1,0x80,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x07,0x07,0x0F,0x0F,0x3F,
0xFF,0xFF,0xFF,0xE0,0x80,0x70,0x38,0x1E,0x07,0x01,0x00,0x84,0x8E,0x8E,0xC6,0xC6,
0xFE,0xFE,0x86,0x86,0x8C,0xFC,0x78,0x01,0x03,0x06,0x1C,0x38,0x00,0xC0,0xFF,0xFF,
0xFF,0x3F,0x0F,0x0F,0x07,0x07,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x3E,0x3E,0x3F,
0x1F,0x1F,0x0F,0x1F,0x3F,0xFE,0xFC,0xF8,0xF0,0xE0,0xE7,0xDF,0xD9,0xD8,0x98,0x98,
0x98,0xF8,0xF0,0xC1,0xC1,0xC1,0xE0,0xE0,0xF0,0xF8,0xFC,0xFE,0x3F,0x1F,0x0F,0x1F,
0x1F,0x3F,0x3E,0x3E,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x0C,0x1E,0x1F,0x3F,0x1F,0x0F,0x07,0x03,0x03,0x03,0x07,0x7F,0xFF,
0xFF,0xFF,0x7F,0x1F,0x7B,0xE3,0xC3,0x03,0x0F,0x1F,0x3F,0x3F,0x1E,0x0C,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0xF0,0x30,0x30,0x30,0x30,0x00,0x80,
0x80,0x00,0x80,0x00,0x00,0x80,0xC0,0xC0,0x80,0x80,0x00,0x00,0x80,0xC0,0xC0,0x80,
0x80,0x00,0xF0,0xF0,0x00,0x00,0x01,0x00,0xB0,0xB0,0x00,0x00,0x80,0x80,0x80,0x80,
0x80,0x00,0x00,0xF0,0xF0,0x00,0x80,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x1F,0x03,0x03,0x03,0x00,0x00,0x1F,
0x1F,0x01,0x01,0x00,0x0F,0x1F,0x1A,0x1A,0x1A,0x0B,0x00,0x0F,0x1F,0x1A,0x1A,0x1A,
0x0B,0x00,0x1F,0x1F,0x18,0x18,0x18,0x00,0x1F,0x1F,0x00,0x00,0x1F,0x1F,0x01,0x01,
0x1F,0x1F,0x00,0x1F,0x1F,0x07,0x0F,0x1D,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};

#endif /* BITMAP_H_ */
