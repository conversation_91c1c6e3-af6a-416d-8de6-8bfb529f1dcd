#ifndef __CONTROL_H
#define __CONTROL_H	 //含有第二种界面显示的函数
#include "sys.h"


//定义PID
typedef struct
{
	float Pdat;
	float pout;
	
	float Idat;
	float iout;
	
	float Ddat;
	float dout;
	
	float OUT;
	
	float Error;
	float Last_Error;
}PID;

extern int MotorL_PWM;	//左电机PWM
extern int MotorR_PWM;	//右电机PWM

extern u8 ARMED;
extern PID PID_Speed; 	//定义速度控制PID
extern PID PID_PIT; 		//定义平衡控制PID
extern PID PID_YAW; 		//定义转向控制PID
extern short int Front_SafeAngle;	//前倾安全角度
extern short int Back_SafeAngle;		//后倾安全角度

extern float yaw_gyro_last;
extern float pit_gyro_now,pit_gyro_Last1,pit_gyro_Last2;
extern float yaw_gyro_now;
extern unsigned char Control_stik;

extern unsigned char PickUp_Flag;	//拿起标志位

void PID_Init(void);

void CONTROL(float pit_now,int Speed_tar,s16 yaw_gyro_tar);




#endif


