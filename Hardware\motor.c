#include "motor.h"

void TIM1_PWM_Init(u16 arr,u16 psc)    
{   
	RCC->APB2ENR|=1<<11;	//TIM1时钟使能
	RCC->APB2ENR|=1<<2;       //PORTA时钟使能   
  RCC->APB2ENR|=1<<0;     //开启辅助时钟
	
	GPIOA->CRH&=0XFFFF0FF0;   //PORTA8,11复用输出
	GPIOA->CRH|=0X0000B00B;   //PORTA8,11复用输出
	
	TIM1->ARR=arr;//设定计数器自动重装值 
	TIM1->PSC=psc;//预分频器不分频
	
	TIM1->CCMR2|=6<<12;//CH4 PWM1模式	
	TIM1->CCMR1|=6<<4; //CH1 PWM1模式	
	
	TIM1->CCMR2|=1<<11;//CH4预装载使能	 
	TIM1->CCMR1|=1<<3; //CH1预装载使能	 
	
	TIM1->CCR1=0;				//设置占空比
	TIM1->CCR4=0;				//设置占空比

	TIM1->CR1|=1<<7;  		//ARPE使能自动重装载预装载允许位
	TIM1->CR1|=1<<4;  		//向下计数模式	 

	TIM1->CCER|=1<<0;   	//OC1 输出使能
	TIM1->CCER|=1<<12;   	//OC4 输出使能

	TIM1->BDTR|=1<<15;		//开启OC和OCN输出
			
	TIM1->EGR|=1<<0;  		//初始化所有的寄存器
	TIM1->CR1|=1<<0;  		//使能定时器

}

//电机的刷新速率50-499
void Motor_Init(void)
{	
	TIM1_PWM_Init(4499,1); //3分频,PWM频率=72000000/(1+1)/(4499+1)=8khz
	 
	RCC->APB2ENR|=1<<3;       //GPIOB时钟使能 
	
	GPIOB->CRH&=0X0000FFFF;   //PB12设置为强推挽
	GPIOB->CRH|=0X33330000;     
	GPIOB->ODR|=0xF<<12;      //PB12上拉
	Set_Motor(0,0);	
	
}
///控制电机转速
//1000-2000
void Set_Motor(int Mo1,int Mo2)
{
/////////////////////////////	
	if(Mo1>0)
	{
		ROLL_MOTOR_DIR=1;
		ROLL_MOTOR_PWM=Mo1;
	}
	else
	{
		ROLL_MOTOR_DIR=0;
		ROLL_MOTOR_PWM=-Mo1;
	}
////////////////////////////	
	if(Mo2>0)
	{
		YAW_MOTOR_DIR=0;
		YAW_MOTOR_PWM=Mo2;
	}
	else
	{
		YAW_MOTOR_DIR=1;
		YAW_MOTOR_PWM=-Mo2;
	}
}
