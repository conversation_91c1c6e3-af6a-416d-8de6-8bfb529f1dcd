# 相机云台集成适配文档

## 项目概述

本文档描述了如何将基于图像坐标的目标跟踪系统集成到现有的无刷电机云台控制系统中。

### 系统架构变更

**原系统：** 蓝牙遥控 → PID控制 → 无刷电机PWM输出
**新系统：** 相机图像坐标 + 蓝牙遥控 → 坐标转换 → PID控制 → 无刷电机PWM输出

## 1. 硬件连接

### 串口连接
- **串口3 (USART3)**: 用于接收相机数据
- **波特率**: 9600
- **数据格式**: 8N1
- **中断接收**: 启用RXNE中断

### 数据包格式
```
[0xFF] [0xF8] [X_Low] [X_High] [Y_Low] [Y_High] [Err_X] [Err_Y] [0x01] [0xFE]
   ↑      ↑       ↑       ↑       ↑       ↑       ↑       ↑       ↑      ↑
 帧头1   帧头2   X坐标低位 X坐标高位 Y坐标低位 Y坐标高位  X误差   Y误差   标识   帧尾
```

## 2. 软件架构修改

### 2.1 文件结构变更

```
User/
├── Data_Transfer.c     # 修改：添加相机数据处理
├── Data_Transfer.h     # 修改：添加相机相关定义
├── control.c          # 修改：添加图像控制函数
├── control.h          # 修改：添加函数声明
└── camera_control.c   # 新增：相机控制专用文件
```

### 2.2 关键数据结构

```c
// 相机数据结构
typedef struct {
    uint16_t center_x;      // 中心X坐标
    uint16_t center_y;      // 中心Y坐标
    int8_t err_x;           // X方向误差
    int8_t err_y;           // Y方向误差
    uint8_t valid;          // 数据有效标志
} CameraData_t;

// 数据点结构
typedef struct {
    int16_t x;
    int16_t y;
    uint8_t num;
} _DATAPoint;
```

## 3. 坐标转换算法

### 3.1 转换参数
```c
#define ImageToActual (1.93f)    // 像素到实际距离转换系数 (mm/pixel)
#define Distance (1000.0f)       // 摄像头到屏幕距离 (mm)
#define Alpha (0.995f)           // 坐标转换补偿系数
#define SCREEN_CENTER_X (320)    // 屏幕中心X坐标
#define SCREEN_CENTER_Y (240)    // 屏幕中心Y坐标
```

### 3.2 转换公式
```
偏移量 = 目标坐标 - 屏幕中心
角度 = atan2(偏移量 × ImageToActual, Distance) × Alpha
目标角度 = 角度 × 180° / π
```

## 4. 控制策略

### 4.1 双模式控制
- **自动跟踪模式**: 有效相机数据时使用图像坐标控制
- **手动遥控模式**: 无相机数据时使用蓝牙遥控

### 4.2 控制算法
- **PITCH轴**: 位置控制（目标角度 - 当前角度）
- **YAW轴**: 角速度控制（角度转换为角速度目标）

## 5. 安全保护机制

### 5.1 数据有效性检查
- 帧头帧尾验证
- 坐标范围检查
- 数据超时保护

### 5.2 角度限制
```c
PITCH: -30° ~ +30°
YAW:   -60° ~ +60°
```

### 5.3 故障回退
- 相机数据超时 → 自动切换到蓝牙控制
- 数据异常 → 保持当前位置

## 6. 调试与标定

### 6.1 参数调整
1. **ImageToActual**: 根据相机分辨率和视场角调整
2. **Distance**: 测量相机到目标的实际距离
3. **Alpha**: 系统补偿系数，通过实测调整

### 6.2 调试工具
- 串口打印坐标和角度信息
- OLED显示当前状态
- LED指示数据接收状态

## 7. 性能指标

### 7.1 响应时间
- 数据接收延迟: < 10ms
- 控制响应时间: < 50ms
- 目标跟踪精度: ±2°

### 7.2 稳定性
- 数据丢包率: < 1%
- 控制稳定性: 无振荡
- 长时间运行: > 8小时

## 8. 故障排除

### 8.1 常见问题
1. **无相机数据**: 检查串口连接和波特率
2. **控制方向错误**: 调整坐标系符号
3. **响应过慢**: 调整角速度转换系数
4. **振荡**: 重新调整PID参数

### 8.2 调试步骤
1. 验证数据接收
2. 检查坐标转换
3. 测试控制响应
4. 优化参数设置

## 9. 版本历史

- **v1.0**: 初始版本，基本功能实现
- **v1.1**: 添加安全保护机制
- **v1.2**: 优化坐标转换算法

## 10. 技术支持

如有问题，请检查：
1. 硬件连接是否正确
2. 软件配置是否完整
3. 参数设置是否合理
4. 调试信息是否正常
