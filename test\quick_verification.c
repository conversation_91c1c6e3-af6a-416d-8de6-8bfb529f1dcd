/**
 * @file quick_verification.c
 * @brief 快速验证测试文件
 * @note 用于验证系统切换后的基本功能
 */

#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "camera_control.h"
#include "data_transfer.h"
#include "control.h"
#include "motor.h"
#include "anbt_dmp_mpu6050.h"
#include "oled.h"

/* ======================== 测试配置 ======================== */

#define ENABLE_QUICK_TEST    // 启用快速测试
#define TEST_DURATION_MS 5000  // 测试持续时间

/* ======================== 测试数据 ======================== */

// 测试用相机数据包
uint8_t test_packets[][10] = {
    // 屏幕中心 (320, 240)
    {0xFF, 0xF8, 0x40, 0x01, 0xF0, 0x00, 0x00, 0x00, 0x01, 0xFE},
    // 右上角 (400, 200)  
    {0xFF, 0xF8, 0x90, 0x01, 0xC8, 0x00, 0x05, 0x03, 0x01, 0xFE},
    // 左下角 (240, 280)
    {0xFF, 0xF8, 0xF0, 0x00, 0x18, 0x01, 0xFB, 0xFD, 0x01, 0xFE},
};

/* ======================== 快速验证函数 ======================== */

/**
 * @brief 系统基础功能验证
 * @return 0-成功, 1-失败
 */
uint8_t Verify_Basic_Functions(void)
{
    printf("\r\n=== 系统基础功能验证 ===\r\n");
    
    // 1. 验证相机控制模块初始化
    reset_receive_state();
    printf("✓ 相机控制模块初始化完成\r\n");
    
    // 2. 验证PID控制器初始化
    if(PID_PIT.Pdat > 0 && PID_YAW.Pdat > 0)
    {
        printf("✓ PID控制器参数正常\r\n");
    }
    else
    {
        printf("✗ PID控制器参数异常\r\n");
        return 1;
    }
    
    // 3. 验证MPU6050传感器
    if(Pitch > -90 && Pitch < 90)
    {
        printf("✓ MPU6050传感器数据正常: Pitch=%.2f°\r\n", Pitch);
    }
    else
    {
        printf("✗ MPU6050传感器数据异常\r\n");
        return 1;
    }
    
    // 4. 验证OLED显示
    OLED_Clear(0);
    OLED_WriteChstr1212(0, 0, "系统测试", 1);
    printf("✓ OLED显示功能正常\r\n");
    
    return 0;
}

/**
 * @brief 相机数据处理验证
 * @return 0-成功, 1-失败
 */
uint8_t Verify_Camera_Data_Processing(void)
{
    printf("\r\n=== 相机数据处理验证 ===\r\n");
    
    for(int i = 0; i < 3; i++)
    {
        printf("测试数据包 %d:\r\n", i + 1);
        
        // 模拟接收数据包
        Camera_ParseData(test_packets[i]);
        
        // 处理数据
        vData_Get();
        
        // 检查解析结果
        if(Camera_IsDataValid())
        {
            printf("  ✓ 数据解析成功: (%d, %d)\r\n", Usartdata.x, Usartdata.y);
            
            // 检查坐标转换
            int16_t coords[1][2] = {{Usartdata.x, Usartdata.y}};
            vImage_To_Gimbal_Angle(coords, 1);
            
            float pitch_angle, yaw_angle;
            Get_Target_Angles(&pitch_angle, &yaw_angle);
            
            printf("  ✓ 角度转换: PITCH=%.2f°, YAW=%.2f°\r\n", 
                   pitch_angle, yaw_angle);
        }
        else
        {
            printf("  ✗ 数据解析失败\r\n");
            return 1;
        }
        
        delay_ms(100);
    }
    
    return 0;
}

/**
 * @brief 控制算法验证
 * @return 0-成功, 1-失败
 */
uint8_t Verify_Control_Algorithm(void)
{
    printf("\r\n=== 控制算法验证 ===\r\n");
    
    // 禁用电机输出进行测试
    uint8_t armed_backup = ARMED;
    ARMED = 0;
    
    // 测试不同目标点的控制输出
    for(int i = 0; i < 3; i++)
    {
        printf("测试目标点 %d:\r\n", i + 1);
        
        // 设置测试数据
        Camera_ParseData(test_packets[i]);
        vData_Get();
        
        if(Camera_IsDataValid())
        {
            // 执行控制算法
            CONTROL_Image_Target(Pitch, Usartdata.x, Usartdata.y);
            
            printf("  目标坐标: (%d, %d)\r\n", Usartdata.x, Usartdata.y);
            printf("  PITCH输出: %.2f\r\n", PID_PIT.OUT);
            printf("  YAW输出: %.2f\r\n", PID_YAW.OUT);
            
            // 检查输出是否在合理范围内
            if(PID_PIT.OUT > -5000 && PID_PIT.OUT < 5000 &&
               PID_YAW.OUT > -5000 && PID_YAW.OUT < 5000)
            {
                printf("  ✓ 控制输出正常\r\n");
            }
            else
            {
                printf("  ✗ 控制输出异常\r\n");
                ARMED = armed_backup;
                return 1;
            }
        }
        
        delay_ms(200);
    }
    
    // 恢复ARMED状态
    ARMED = armed_backup;
    return 0;
}

/**
 * @brief 双模式切换验证
 * @return 0-成功, 1-失败
 */
uint8_t Verify_Dual_Mode_Switch(void)
{
    printf("\r\n=== 双模式切换验证 ===\r\n");
    
    // 1. 测试自动跟踪模式
    printf("测试自动跟踪模式:\r\n");
    Camera_ParseData(test_packets[0]);
    vData_Get();
    
    if(Camera_HasNewData() && Camera_IsDataValid())
    {
        printf("  ✓ 自动跟踪模式激活\r\n");
    }
    else
    {
        printf("  ✗ 自动跟踪模式失败\r\n");
        return 1;
    }
    
    delay_ms(1000);
    
    // 2. 测试手动控制模式（模拟数据超时）
    printf("测试手动控制模式:\r\n");
    
    // 等待数据超时
    delay_ms(600);  // 超过500ms超时时间
    
    if(!Camera_HasNewData())
    {
        printf("  ✓ 手动控制模式激活\r\n");
    }
    else
    {
        printf("  ✗ 手动控制模式失败\r\n");
        return 1;
    }
    
    return 0;
}

/**
 * @brief 显示系统验证
 * @return 0-成功, 1-失败
 */
uint8_t Verify_Display_System(void)
{
    printf("\r\n=== 显示系统验证 ===\r\n");
    
    // 清屏
    OLED_Clear(0);
    
    // 测试基本显示
    OLED_WriteChstr1212(0, 0, "相机云台系统", 1);
    OLED_WriteChstr1212(0, 13, "状态：正常", 1);
    OLED_WriteChstr1212(0, 26, "模式：自动", 1);
    OLED_WriteChstr1212(0, 39, "测试：通过", 1);
    
    printf("✓ 基本显示功能正常\r\n");
    
    // 测试数值显示
    OLED_Display0612Num(80, 13, 1234, 4, 1);
    OLED_Display0612Num1dot(80, 26, 567, 1);
    
    printf("✓ 数值显示功能正常\r\n");
    
    delay_ms(2000);
    
    return 0;
}

/**
 * @brief 运行快速验证测试
 */
void Run_Quick_Verification(void)
{
    #ifdef ENABLE_QUICK_TEST
    
    printf("\r\n");
    printf("========================================\r\n");
    printf("    相机云台系统快速验证测试\r\n");
    printf("========================================\r\n");
    printf("系统时钟: %dMHz\r\n", SystemCoreClock / 1000000);
    printf("测试时间: %dms\r\n", TEST_DURATION_MS);
    printf("========================================\r\n");
    
    uint32_t start_time = time_tick;
    uint8_t test_result = 0;
    
    // 执行各项验证测试
    test_result |= Verify_Basic_Functions();
    test_result |= Verify_Camera_Data_Processing();
    test_result |= Verify_Control_Algorithm();
    test_result |= Verify_Dual_Mode_Switch();
    test_result |= Verify_Display_System();
    
    uint32_t end_time = time_tick;
    uint32_t elapsed_time = (end_time - start_time) * 5;  // 5ms per tick
    
    printf("\r\n========================================\r\n");
    printf("测试完成！\r\n");
    printf("总耗时: %dms\r\n", elapsed_time);
    
    if(test_result == 0)
    {
        printf("✓ 所有测试通过 - 系统切换成功！\r\n");
        
        // 在OLED上显示成功信息
        OLED_Clear(0);
        OLED_WriteChstr1212(20, 10, "系统切换", 1);
        OLED_WriteChstr1212(30, 25, "成功！", 1);
        OLED_WriteChstr1212(10, 40, "所有测试通过", 1);
    }
    else
    {
        printf("✗ 部分测试失败 - 请检查系统配置！\r\n");
        
        // 在OLED上显示失败信息
        OLED_Clear(0);
        OLED_WriteChstr1212(20, 10, "系统测试", 1);
        OLED_WriteChstr1212(30, 25, "失败！", 1);
        OLED_WriteChstr1212(10, 40, "请检查配置", 1);
    }
    
    printf("========================================\r\n");
    
    #endif
}

/**
 * @brief 快速验证初始化
 */
void Quick_Verification_Init(void)
{
    // 初始化串口用于调试输出
    printf("快速验证测试初始化...\r\n");
    
    // 初始化相机控制系统
    reset_receive_state();
    
    // 等待系统稳定
    delay_ms(1000);
    
    printf("快速验证测试准备就绪\r\n");
}
