/**
 * @file UI_enhanced.c
 * @brief 增强版UI显示，添加相机数据显示功能
 * @note 在原有显示基础上增加相机跟踪状态显示，不影响原有功能
 */

#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "anbt_dmp_mpu6050.h"
#include "encoder.h"
#include "control.h"
#include "motor.h"
#include "led.h"
#include "DMA.h"
#include "data_transfer.h"
#include "camera_control.h"  // 新增：相机控制头文件
#include "stmflash.h"
#include "oled.h"
#include "ADC.h"
#include "UI.h"

/* ======================== 相机显示相关变量 ======================== */

// 相机状态显示字符串（使用英文避免编码问题）
unsigned char CameraUI_String[3][4][8] = {
    "CAM:", "TGT:", "ANG:", "MOD:",
    "CAM:", "TGT:", "ANG:", "MOD:",
    "CAM:", "TGT:", "ANG:", "MOD:",
};

// 控制模式显示字符串
unsigned char ControlMode_String[3][2][6] = {
    "AUTO", "MANU",
    "AUTO", "MANU",
    "AUTO", "MANU",
};

// 相机状态显示字符串
unsigned char CameraStatus_String[3][3][6] = {
    "OK  ", "TOUT", "INVD",
    "OK  ", "TOUT", "INVD",
    "OK  ", "TOUT", "INVD",
};

/* ======================== 增强版显示函数 ======================== */

/**
 * @brief 显示相机数据信息
 * @param x: X坐标位置
 * @param y: Y坐标位置
 */
void Display_Camera_Info(uint8_t x, uint8_t y)
{
    // 显示相机状态
    OLED_WriteChstr1212(x, y, CameraUI_String[0][0], 1);  // 使用固定语言索引

    if(Camera_HasNewData())
    {
        OLED_WriteChstr1212(x+30, y, CameraStatus_String[0][0], 1);  // "OK"
    }
    else if(time_tick > 100)  // 简化超时检查
    {
        OLED_WriteChstr1212(x+30, y, CameraStatus_String[0][1], 1);  // "TOUT"
    }
    else
    {
        OLED_WriteChstr1212(x+30, y, CameraStatus_String[0][2], 1);  // "INVD"
    }
}

/**
 * @brief 显示目标坐标信息
 * @param x: X坐标位置
 * @param y: Y坐标位置
 */
void Display_Target_Coordinates(uint8_t x, uint8_t y)
{
    // 显示目标坐标
    OLED_WriteChstr1212(x, y, CameraUI_String[Lang_x][1], 1);
    
    if(Camera_IsDataValid())
    {
        // 显示X坐标
        OLED_Display0612Num(x+30, y, Usartdata.x, 3, 1);
        OLED_WriteChstr1212(x+54, y, ",", 1);
        // 显示Y坐标
        OLED_Display0612Num(x+60, y, Usartdata.y, 3, 1);
    }
    else
    {
        OLED_WriteChstr1212(x+30, y, "---,---", 1);
    }
}

/**
 * @brief 显示目标角度信息
 * @param x: X坐标位置
 * @param y: Y坐标位置
 */
void Display_Target_Angles(uint8_t x, uint8_t y)
{
    float pitch_angle, yaw_angle;
    
    // 显示角度标签
    OLED_WriteChstr1212(x, y, CameraUI_String[Lang_x][2], 1);
    
    // 获取目标角度
    Get_Target_Angles(&pitch_angle, &yaw_angle);
    
    if(Camera_IsDataValid())
    {
        // 显示PITCH角度
        OLED_Display0612Num1dot(x+30, y, (int16_t)(pitch_angle * 10), 1);
        OLED_WriteChstr1212(x+54, y, ",", 1);
        // 显示YAW角度
        OLED_Display0612Num1dot(x+60, y, (int16_t)(yaw_angle * 10), 1);
    }
    else
    {
        OLED_WriteChstr1212(x+30, y, "---.--", 1);
    }
}

/**
 * @brief 显示控制模式信息
 * @param x: X坐标位置
 * @param y: Y坐标位置
 */
void Display_Control_Mode(uint8_t x, uint8_t y)
{
    // 显示模式标签
    OLED_WriteChstr1212(x, y, CameraUI_String[Lang_x][3], 1);
    
    // 显示当前控制模式
    if(Camera_HasNewData() && Camera_IsDataValid())
    {
        OLED_WriteChstr1212(x+30, y, ControlMode_String[Lang_x][0], 1);  // "自动"
    }
    else
    {
        OLED_WriteChstr1212(x+30, y, ControlMode_String[Lang_x][1], 1);  // "手动"
    }
}

/* ======================== 增强版模式显示函数 ======================== */

/**
 * @brief 增强版平衡模式显示
 * @note 在原有显示基础上添加相机信息显示
 */
void Balance_Mode_Enhanced(void)
{
    if(UI_Display_Flag==1)
    {
        OLED_Clear(0);
        OLED_WriteChstr1212(24,0,Title_Strings[WorkMode][Lang_x],1);
        
        // 原有的基础信息显示（左侧）
        OLED_WriteChstr1212(0,13,StandbyUI_String[Lang_x][0],1);   // "角度："
        OLED_WriteChstr1212(0,26,StandbyUI_String[Lang_x][2],1);   // "左速："
        OLED_WriteChstr1212(0,39,StandbyUI_String[Lang_x][4],1);   // "电压："
        
        // 相机信息显示（右侧）
        OLED_WriteChstr1212(66,13, CameraUI_String[Lang_x][0], 1); // "相机："
        OLED_WriteChstr1212(66,26, CameraUI_String[Lang_x][1], 1); // "目标："
        OLED_WriteChstr1212(66,39, CameraUI_String[Lang_x][3], 1); // "模式："
        
        // 底部提示信息
        OLED_WriteChstr1212(0,52,StandbyUI_String2[WorkMode][Lang_x],1);
        
        LED_state=LED_SHORTFLASH;ARMED=1;time_tick=0;UI_Display_Flag=0;
    }
    
    // 按键处理（保持原有逻辑）
    if(Key_C_sta==1)
    {
        time_tick=0;
        Key_C_sta=0;
    }
    
    // 动态数据更新
    // 左侧：原有数据
    OLED_Display0612Num1dot(32,13,Pitch*10,1);              // 显示俯仰角度
    OLED_Display0612Num(32,26,MotorL_PWM,5,1);              // 显示左电机PWM
    OLED_Display0507Num1dot(30,39,Volt);                    // 显示电池电压
    
    // 右侧：相机数据
    // 相机状态显示
    if(Camera_HasNewData())
    {
        OLED_WriteChstr1212(98,13, CameraStatus_String[Lang_x][0], 1);  // "正常"
    }
    else
    {
        OLED_WriteChstr1212(98,13, CameraStatus_String[Lang_x][1], 1);  // "超时"
    }
    
    // 目标坐标显示（简化版）
    if(Camera_IsDataValid())
    {
        OLED_Display0612Num(98,26, Usartdata.x, 3, 1);
    }
    else
    {
        OLED_WriteChstr1212(98,26, "---", 1);
    }
    
    // 控制模式显示
    if(Camera_HasNewData() && Camera_IsDataValid())
    {
        OLED_WriteChstr1212(98,39, ControlMode_String[Lang_x][0], 1);   // "自动"
    }
    else
    {
        OLED_WriteChstr1212(98,39, ControlMode_String[Lang_x][1], 1);   // "手动"
    }
}

/**
 * @brief 增强版待机模式显示
 * @note 在原有显示基础上添加相机状态显示
 */
void Standby_Mode_Enhanced(void)
{
    if(UI_Display_Flag==1)
    {
        OLED_Clear(0);
        OLED_WriteChstr1212(24,0,Title_Strings[WorkMode][Lang_x],1);
        
        // 原有显示内容
        OLED_WriteChstr1212(0,13,StandbyUI_String[Lang_x][0],1);    OLED_WriteChstr1212(66,13,StandbyUI_String[Lang_x][1],1);
        OLED_WriteChstr1212(0,26,StandbyUI_String[Lang_x][2],1);    OLED_WriteChstr1212(66,26,StandbyUI_String[Lang_x][3],1);
        OLED_WriteChstr1212(0,39,StandbyUI_String[Lang_x][4],1);    
        
        // 在右下角显示相机状态
        OLED_WriteChstr1212(66,39, CameraUI_String[Lang_x][0], 1);  // "相机："
        
        OLED_WriteChstr1212(0,52,StandbyUI_String2[WorkMode][Lang_x],1);
        LED_state=LED_LONGFLASH;UI_Display_Flag=0;
    }
    
    // 按键处理（保持原有逻辑）
    if(Key_C_sta==1)
    {
        WorkMode=0x01;
        UI_Display_Flag=1;
        Key_C_sta=0;
    }
    
    // 动态数据更新
    OLED_Display0612Num1dot(32,13,Pitch*10,1);    OLED_Display0612Num1dot(98,13,yaw_gyro_now*10,1);
    OLED_Display0612Num(32,26,MotorL_PWM,5,1);    OLED_Display0612Num(98,26,MotorR_PWM,5,1);
    OLED_Display0507Num1dot(30,39,Volt);
    
    // 相机状态显示
    if(Camera_HasNewData())
    {
        OLED_WriteChstr1212(98,39, CameraStatus_String[Lang_x][0], 1);  // "正常"
    }
    else
    {
        OLED_WriteChstr1212(98,39, CameraStatus_String[Lang_x][2], 1);  // "无效"
    }
    
    // 蓝牙状态显示（保持原有逻辑）
    if(BlueCK==0)    OLED_WriteChstr1212(98,52,StandbyUI_String[Lang_x][6],1);
    else    OLED_WriteChstr1212(98,52,StandbyUI_String[Lang_x][7],1);
}

/* ======================== 新增：相机调试模式 ======================== */

/**
 * @brief 相机调试模式显示
 * @note 专门用于显示详细的相机数据，便于调试
 */
void Camera_Debug_Mode(void)
{
    if(UI_Display_Flag==1)
    {
        OLED_Clear(0);
        OLED_WriteChstr1212(30,0,"[ 相机调试 ]",1);
        
        // 显示标签
        OLED_WriteChstr1212(0,13, "坐标：", 1);
        OLED_WriteChstr1212(0,26, "误差：", 1);
        OLED_WriteChstr1212(0,39, "角度：", 1);
        OLED_WriteChstr1212(0,52, "状态：", 1);
        
        UI_Display_Flag=0;
    }
    
    // 动态数据更新
    if(Camera_IsDataValid())
    {
        // 显示坐标
        OLED_Display0612Num(30,13, Usartdata.x, 3, 1);
        OLED_WriteChstr1212(54,13, ",", 1);
        OLED_Display0612Num(60,13, Usartdata.y, 3, 1);
        
        // 显示误差
        CameraData_t* cam_data = Camera_GetData();
        OLED_Display0612Num(30,26, cam_data->err_x, 3, 1);
        OLED_WriteChstr1212(54,26, ",", 1);
        OLED_Display0612Num(60,26, cam_data->err_y, 3, 1);
        
        // 显示角度
        float pitch_angle, yaw_angle;
        Get_Target_Angles(&pitch_angle, &yaw_angle);
        OLED_Display0612Num1dot(30,39, (int16_t)(pitch_angle * 10), 1);
        OLED_WriteChstr1212(54,39, ",", 1);
        OLED_Display0612Num1dot(60,39, (int16_t)(yaw_angle * 10), 1);
        
        // 显示状态
        OLED_WriteChstr1212(30,52, "跟踪中", 1);
    }
    else
    {
        OLED_WriteChstr1212(30,13, "---,---", 1);
        OLED_WriteChstr1212(30,26, "---,---", 1);
        OLED_WriteChstr1212(30,39, "---,---", 1);
        OLED_WriteChstr1212(30,52, "等待中", 1);
    }
    
    // 按键退出
    if(Key_C_sta==1)
    {
        WorkMode=0x01;  // 返回平衡模式
        UI_Display_Flag=1;
        Key_C_sta=0;
    }
}
