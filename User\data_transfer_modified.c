#include "data_transfer.h"
#include "camera_control.h"  // 新增：相机控制头文件
#include "dma.h"
#include "stmflash.h"
#include "anbt_dmp_mpu6050.h"
#include "oled.h"
#include "encoder.h"
#include "UI.h"

/* ======================== 全局变量定义 ======================== */

// 数据发送缓冲区
unsigned char data_to_send[32];

// 原有蓝牙遥控数据
short int Blue_RC[3];
unsigned char Blue_dat[UART_RECV_LEN];		//串口3接收缓存（保留作为备用）

// 编码器相关
int LeftKnob=0,RightKnob=0;		//左右轮旋转脉冲计数

// 数据发送标志
unsigned char Send_Attitude=0;//发送姿态数据标记变量
unsigned char Send_Senser=0;	//发送传感器数据标记变量
unsigned char Send_RCData=0;	//发送遥控数据标记变量
unsigned char Send_PID1=0;		//发送PID数据标记变量
unsigned int time_tick=0;			//Time计数器
unsigned char Control_Flag=0;

/* ======================== 原有蓝牙数据处理函数 ======================== */

/**
 * @brief 蓝牙数据接收分析（保留作为备用控制方式）
 * @note 解析蓝牙遥控器发送的控制数据
 */
void BlueData_Receive_Anl(void)
{
	unsigned char startx,nx;
	for(startx=0;startx<UART_RECV_LEN-1;startx++)
	{
		if(Blue_dat[startx]==0xAA && Blue_dat[startx+1]==0xAA)	//是数值帧头
		{//BlueCK=250;
			 break;
		}
	}
	if( (UART_RECV_LEN-startx >=8) && Blue_dat[startx+6]==0x55 && Blue_dat[startx+7]==0x55)	//是有效数据
	{
		BlueCK=250;
		if(Blue_dat[startx+4]&0x80)	//为负数
		{
			Blue_RC[1]=0-(256-Blue_dat[startx+4]);
		}
		else
		{
			Blue_RC[1]=Blue_dat[startx+4];
		}
		if(Blue_dat[startx+5]&0x80)	//为负数
		{
			Blue_RC[0]=0-(256-Blue_dat[startx+5]);
		}
		else
		{
			Blue_RC[0]=Blue_dat[startx+5];
		}
		for(nx=0;nx<UART_RECV_LEN;nx++)	Blue_dat[nx]=0;		
	}
	else
	{
		if(BlueCK>0) BlueCK--;
	}
}

/* ======================== 数据发送函数 ======================== */

/**
 * @brief 发送姿态数据
 */
void Data_Send_Attitude(void)
{
	u8 sum,point;	 
	s16 angle;	
	point=0;
	data_to_send[point++] = 0xAA;data_to_send[point++] = 0xAA;data_to_send[point++] = 0x01;data_to_send[point++] = 0;
	angle = (s16)(Pitch*100);	data_to_send[point++] = angle>>8;	data_to_send[point++] = angle&0xFF;
	angle = (s16)(MPU6050_accel[1]);	data_to_send[point++] = angle>>8;	data_to_send[point++] = angle&0xFF;
	angle = (s16)(MPU6050_accel[2]);	data_to_send[point++] = angle>>8;	data_to_send[point++] = angle&0xFF;
	if(ARMED == 1)	data_to_send[point++]= 0xA1;	//解锁
	else				data_to_send[point++]= 0xA0;//锁定
	data_to_send[point++]= 0xA0;
	data_to_send[3] = point-4;//数据长度
	sum=0;
	for(angle=0;angle<point;angle++)//校验和
		sum += data_to_send[angle];
	data_to_send[point++] = sum;//发送校验和
	for(angle=0;angle<point;angle++)//校验和
		USART3_SendData(data_to_send[angle]);
}

/**
 * @brief 发送PID参数数据
 */
void Data_Send_PID1(void)
{
	u8 sum,point;
	u16 temp;
	point=0;
	data_to_send[point++]=0xAA;data_to_send[point++]=0xAA;data_to_send[point++]=0x10;data_to_send[point++]=0;	
	temp = PID_Speed.Pdat; 	data_to_send[point++]=temp>>8; 	data_to_send[point++]=temp&0xFF;
	temp = PID_Speed.Idat;	data_to_send[point++]=temp>>8; 	data_to_send[point++]=temp&0xFF;
	temp = PID_Speed.Ddat;	data_to_send[point++]=temp>>8;	data_to_send[point++]=temp&0xFF;
	
	temp = PID_PIT.Pdat;	data_to_send[point++]=temp>>8;	data_to_send[point++]=temp&0xFF;
	temp = PID_PIT.Idat;	data_to_send[point++]=temp>>8;	data_to_send[point++]=temp&0xFF;
	temp = PID_PIT.Ddat;	data_to_send[point++]=temp>>8;	data_to_send[point++]=temp&0xFF;
	
	temp = PID_YAW.Pdat;	data_to_send[point++]=temp>>8;	data_to_send[point++]=temp&0xFF;
	temp = PID_YAW.Idat;	data_to_send[point++]=temp>>8;	data_to_send[point++]=temp&0xFF;
	temp = PID_YAW.Ddat;	data_to_send[point++]=temp>>8;	data_to_send[point++]=temp&0xFF;
	data_to_send[3] = point-4;//数据长度
	sum=0;
	for(temp=0;temp<point;temp++)//校验和
		sum += data_to_send[temp];
	data_to_send[point++] = sum;//发送校验和

	for(temp=0;temp<point;temp++)//校验和
		USART3_SendData(data_to_send[temp]);
}

/**
 * @brief 数据发送调度
 */
void Send_Data(void)
{
	if(Send_PID1)
	{
		Send_PID1 = 0;
		Data_Send_PID1();	//发送PID
		Send_Attitude = 0;
	}
	else if(Send_Attitude)
	{
		Send_Attitude = 0;
		Data_Send_Attitude();	//发送姿态
	}
}

/* ======================== Flash存储函数 ======================== */

/**
 * @brief 写入陀螺仪偏移值到Flash
 */
void Flash_Save_GyroOffset(long *Gyro_offset)
{
	u8 point1=0;
	unsigned short int GyroOffset_data[10];
	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + GYRO_OFFSET_FLASHADR, GyroOffset_data, 10);

 	GyroOffset_data[point1++] = (short int)((Gyro_offset[0]>>16)&0xFFFF);
	GyroOffset_data[point1++] = (short int)(Gyro_offset[0]&0xFFFF);

  GyroOffset_data[point1++] = (short int)((Gyro_offset[1]>>16)&0xFFFF);
	GyroOffset_data[point1++] = (short int)(Gyro_offset[1]&0xFFFF);

 	GyroOffset_data[point1++] = (short int)((Gyro_offset[2]>>16)&0xFFFF);
	GyroOffset_data[point1++] = (short int)(Gyro_offset[2]&0xFFFF);

	//写入Flash数据
	STMFLASH_Write(FLASH_SAVE_ADDR + GYRO_OFFSET_FLASHADR,GyroOffset_data,10);
}

/**
 * @brief 从Flash读取陀螺仪偏移值
 */
void Flash_Read_GyroOffset(long *Gyro_offset)
{
	u8 point2=0;
	unsigned short int GyroOffset_readdata[10];
	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + GYRO_OFFSET_FLASHADR,GyroOffset_readdata,10);

	Gyro_offset[0]=(long)GyroOffset_readdata[point2++];
	Gyro_offset[0]=(Gyro_offset[0]<<16 )| (long)GyroOffset_readdata[point2++];

	Gyro_offset[1]=(long)GyroOffset_readdata[point2++];
	Gyro_offset[1]=(Gyro_offset[1]<<16 )| (long)GyroOffset_readdata[point2++];

	Gyro_offset[2]=(long)GyroOffset_readdata[point2++];
	Gyro_offset[2]=(Gyro_offset[2]<<16 )| (long)GyroOffset_readdata[point2++];
}

/**
 * @brief 写入加速度计偏移值到Flash
 */
void Flash_Save_AccOffset(long *Accel_offset)
{
	u8 point3=0;
	unsigned short int AccOffset_data[10];
	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + ACC_OFFSET_FLASHADR, AccOffset_data,10);

	AccOffset_data[point3++] = (unsigned short int)((Accel_offset[0]>>16)&0xFFFF);
	AccOffset_data[point3++] = (unsigned short int)(Accel_offset[0]&0xFFFF);

	AccOffset_data[point3++] = (unsigned short int)((Accel_offset[1]>>16)&0xFFFF);
	AccOffset_data[point3++] = (unsigned short int)(Accel_offset[1]&0xFFFF);

	AccOffset_data[point3++] = (unsigned short int)((Accel_offset[2]>>16)&0xFFFF);
	AccOffset_data[point3++] = (unsigned short int)(Accel_offset[2]&0xFFFF);

	//写入Flash数据
	STMFLASH_Write(FLASH_SAVE_ADDR + ACC_OFFSET_FLASHADR,AccOffset_data,10);
}

/**
 * @brief 从Flash读取加速度计偏移值
 */
void Flash_Read_AccOffset(long *Accel_offset)
{
	u8 point4=0;
	unsigned short int AccOffset_readdata[10];
	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + ACC_OFFSET_FLASHADR, AccOffset_readdata,10);

	Accel_offset[0]=(long)AccOffset_readdata[point4++];
	Accel_offset[0]=(Accel_offset[0]<<16 )| (long)AccOffset_readdata[point4++];

	Accel_offset[1]=(long)AccOffset_readdata[point4++];
	Accel_offset[1]=(Accel_offset[1]<<16 )| (long)AccOffset_readdata[point4++];

	Accel_offset[2]=(long)AccOffset_readdata[point4++];
	Accel_offset[2]=(Accel_offset[2]<<16 )| (long)AccOffset_readdata[point4++];
}

/**
 * @brief 写入PID参数到Flash
 */
void Flash_Save_PID(void)
{
	u8 point5=0;
	unsigned short int PIDflash_data[10];

	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + PID_FLASHADR ,PIDflash_data,10);

	PIDflash_data[point5++] = (s16)(PID_Speed.Pdat);
	PIDflash_data[point5++] = (s16)(PID_Speed.Idat);
	PIDflash_data[point5++] = (s16)(PID_Speed.Ddat);

	PIDflash_data[point5++] = (s16)(PID_PIT.Pdat);
	PIDflash_data[point5++] = (s16)(PID_PIT.Idat);
	PIDflash_data[point5++] = (s16)(PID_PIT.Ddat);

	PIDflash_data[point5++] = (s16)(PID_YAW.Pdat);
	PIDflash_data[point5++] = (s16)(PID_YAW.Idat);
	PIDflash_data[point5++] = (s16)(PID_YAW.Ddat);

	//写入Flash数据
	STMFLASH_Write(FLASH_SAVE_ADDR + PID_FLASHADR ,PIDflash_data,10);
}

/**
 * @brief 从Flash读取PID参数
 */
void Flash_Read_PID(void)
{
	u8 point6=0;
	unsigned short int PIDflash_readdata[10];
	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + PID_FLASHADR ,PIDflash_readdata,10);

	//读出当前PID值
	PID_Speed.Pdat = (float)PIDflash_readdata[point6++];
	PID_Speed.Idat = (float)PIDflash_readdata[point6++];
	PID_Speed.Ddat = (float)PIDflash_readdata[point6++];

	PID_PIT.Pdat = (float)PIDflash_readdata[point6++];
	PID_PIT.Idat = (float)PIDflash_readdata[point6++];
	PID_PIT.Ddat = (float)PIDflash_readdata[point6++];

	PID_YAW.Pdat = (float)PIDflash_readdata[point6++];
	PID_YAW.Idat = (float)PIDflash_readdata[point6++];
	PID_YAW.Ddat = (float)PIDflash_readdata[point6++];
}

/**
 * @brief 写入安全角度值到Flash
 */
void Flash_Save_SafeAngle(void)
{
	u8 point7=0;
	unsigned short int SafeAngle_data[10];
	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + SAFEANGLE_FLASHADR ,SafeAngle_data,10);

	SafeAngle_data[point7++] =Front_SafeAngle;
	SafeAngle_data[point7++] =Back_SafeAngle;

	//写入Flash数据
	STMFLASH_Write(FLASH_SAVE_ADDR + SAFEANGLE_FLASHADR ,SafeAngle_data,10);
}

/**
 * @brief 从Flash读取安全角度值
 */
void Flash_Read_SafeAngle(void)
{
	u8 point8=0;
	unsigned short int SafeAngle_readdata[10];
	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + SAFEANGLE_FLASHADR ,SafeAngle_readdata,10);

	Front_SafeAngle=SafeAngle_readdata[point8++];
	Back_SafeAngle=SafeAngle_readdata[point8++];
}

/**
 * @brief 写入语言设置到Flash
 */
void Flash_Save_Language(void)
{
	u8 point9=0;
	unsigned short int Language_data[10];
	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + LANGUAGE_FLASHADR ,Language_data,10);

	Language_data[point9++] =Lang_x;
	//写入Flash数据
	STMFLASH_Write(FLASH_SAVE_ADDR + LANGUAGE_FLASHADR ,Language_data,10);
}

/**
 * @brief 从Flash读取语言设置
 */
void Flash_Read_Language(void)
{
	u8 point10=0;
	unsigned short int Language_readdata[10];
	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + LANGUAGE_FLASHADR ,Language_readdata,10);

	Lang_x=Language_readdata[point10++];
	if(Lang_x>2 || Lang_x<0) Lang_x=0;
}

/**
 * @brief 写入低电压报警值到Flash
 */
void Flash_Save_VoltWarn(void)
{
	u8 point11=0;
	unsigned short int VoltWarn_data[10];
	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + VOLTWARN_FLASHADR ,VoltWarn_data,10);

	VoltWarn_data[point11++] =VoltLowWarn;
	//写入Flash数据
	STMFLASH_Write(FLASH_SAVE_ADDR + VOLTWARN_FLASHADR ,VoltWarn_data,10);
}

/**
 * @brief 从Flash读取低电压报警值
 */
void Flash_Read_VoltWarn(void)
{
	u8 point12=0;
	unsigned short int VoltWarn_readdata[10];
	//读出Flash数据
	STMFLASH_Read(FLASH_SAVE_ADDR + VOLTWARN_FLASHADR ,VoltWarn_readdata,10);

	VoltLowWarn=VoltWarn_readdata[point12++];
	if(VoltLowWarn>120 || VoltLowWarn<65) VoltLowWarn=70;
}

/**
 * @brief Flash保存延时处理
 */
void Flash_Save_Delay(void)
{
	EXTI->IMR&=~(1<<3);//  关闭启line BITx上的中断
	delay_ms(900);
	EXTI->IMR|=1<<3;//  开启line BITx上的中断
}
