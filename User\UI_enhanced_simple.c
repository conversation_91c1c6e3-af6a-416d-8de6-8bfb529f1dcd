/**
 * @file UI_enhanced_simple.c
 * @brief 简化版增强UI显示，避免编译问题
 * @note 在原有显示基础上增加相机跟踪状态显示
 */

#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "anbt_dmp_mpu6050.h"
#include "encoder.h"
#include "control.h"
#include "motor.h"
#include "led.h"
#include "DMA.h"
#include "data_transfer.h"
#include "camera_control.h"
#include "stmflash.h"
#include "oled.h"
#include "ADC.h"
#include "UI.h"

/* ======================== 简化版增强显示函数 ======================== */

/**
 * @brief 增强版平衡模式显示
 * @note 在原有显示基础上添加相机信息显示
 */
void Balance_Mode_Enhanced(void)
{
    if(UI_Display_Flag==1)
    {
        OLED_Clear(0);
        OLED_WriteChstr1212(24,0,Title_Strings[WorkMode][Lang_x],1);
        
        // 原有的基础信息显示（左侧）
        OLED_WriteChstr1212(0,13,StandbyUI_String[Lang_x][0],1);   // "角度："
        OLED_WriteChstr1212(0,26,StandbyUI_String[Lang_x][2],1);   // "左速："
        OLED_WriteChstr1212(0,39,StandbyUI_String[Lang_x][4],1);   // "电压："
        
        // 相机信息显示（右侧）
        OLED_WriteChstr1212(66,13, "CAM:", 1);  // 相机状态
        OLED_WriteChstr1212(66,26, "TGT:", 1);  // 目标坐标
        OLED_WriteChstr1212(66,39, "MOD:", 1);  // 控制模式
        
        // 底部提示信息
        OLED_WriteChstr1212(0,52,StandbyUI_String2[WorkMode][Lang_x],1);
        
        LED_state=LED_SHORTFLASH;ARMED=1;time_tick=0;UI_Display_Flag=0;
    }
    
    // 按键处理（保持原有逻辑）
    if(Key_C_sta==1)
    {
        time_tick=0;
        Key_C_sta=0;
    }
    
    // 动态数据更新
    // 左侧：原有数据
    OLED_Display0612Num1dot(32,13,Pitch*10,1);              // 显示俯仰角度
    OLED_Display0612Num(32,26,MotorL_PWM,5,1);              // 显示左电机PWM
    OLED_Display0507Num1dot(30,39,Volt);                    // 显示电池电压
    
    // 右侧：相机数据
    // 相机状态显示
    if(Camera_HasNewData())
    {
        OLED_WriteChstr1212(98,13, "OK", 1);   // 正常
    }
    else
    {
        OLED_WriteChstr1212(98,13, "NO", 1);   // 无数据
    }
    
    // 目标坐标显示（简化版）
    if(Camera_IsDataValid())
    {
        OLED_Display0612Num(98,26, Usartdata.x, 3, 1);
    }
    else
    {
        OLED_WriteChstr1212(98,26, "---", 1);
    }
    
    // 控制模式显示
    if(Camera_HasNewData() && Camera_IsDataValid())
    {
        OLED_WriteChstr1212(98,39, "AUTO", 1);   // 自动
    }
    else
    {
        OLED_WriteChstr1212(98,39, "MANU", 1);   // 手动
    }
}

/**
 * @brief 增强版待机模式显示
 * @note 在原有显示基础上添加相机状态显示
 */
void Standby_Mode_Enhanced(void)
{
    if(UI_Display_Flag==1)
    {
        OLED_Clear(0);
        OLED_WriteChstr1212(24,0,Title_Strings[WorkMode][Lang_x],1);
        
        // 原有显示内容
        OLED_WriteChstr1212(0,13,StandbyUI_String[Lang_x][0],1);    
        OLED_WriteChstr1212(66,13,StandbyUI_String[Lang_x][1],1);
        OLED_WriteChstr1212(0,26,StandbyUI_String[Lang_x][2],1);    
        OLED_WriteChstr1212(66,26,StandbyUI_String[Lang_x][3],1);
        OLED_WriteChstr1212(0,39,StandbyUI_String[Lang_x][4],1);    
        
        // 在右下角显示相机状态
        OLED_WriteChstr1212(66,39, "CAM:", 1);
        
        OLED_WriteChstr1212(0,52,StandbyUI_String2[WorkMode][Lang_x],1);
        LED_state=LED_LONGFLASH;UI_Display_Flag=0;
    }
    
    // 按键处理（保持原有逻辑）
    if(Key_C_sta==1)
    {
        WorkMode=0x01;
        UI_Display_Flag=1;
        Key_C_sta=0;
    }
    
    // 动态数据更新
    OLED_Display0612Num1dot(32,13,Pitch*10,1);    
    OLED_Display0612Num1dot(98,13,yaw_gyro_now*10,1);
    OLED_Display0612Num(32,26,MotorL_PWM,5,1);    
    OLED_Display0612Num(98,26,MotorR_PWM,5,1);
    OLED_Display0507Num1dot(30,39,Volt);
    
    // 相机状态显示
    if(Camera_HasNewData())
    {
        OLED_WriteChstr1212(98,39, "OK", 1);   // 正常
    }
    else
    {
        OLED_WriteChstr1212(98,39, "NO", 1);   // 无效
    }
    
    // 蓝牙状态显示（保持原有逻辑）
    if(BlueCK==0)    
        OLED_WriteChstr1212(98,52,StandbyUI_String[Lang_x][6],1);
    else    
        OLED_WriteChstr1212(98,52,StandbyUI_String[Lang_x][7],1);
}

/**
 * @brief 相机调试模式显示
 * @note 专门用于显示详细的相机数据，便于调试
 */
void Camera_Debug_Mode(void)
{
    if(UI_Display_Flag==1)
    {
        OLED_Clear(0);
        OLED_WriteChstr1212(30,0,"[ CAM DEBUG ]",1);
        
        // 显示标签
        OLED_WriteChstr1212(0,13, "XY:", 1);
        OLED_WriteChstr1212(0,26, "ERR:", 1);
        OLED_WriteChstr1212(0,39, "ANG:", 1);
        OLED_WriteChstr1212(0,52, "STA:", 1);
        
        UI_Display_Flag=0;
    }
    
    // 动态数据更新
    if(Camera_IsDataValid())
    {
        // 显示坐标
        OLED_Display0612Num(30,13, Usartdata.x, 3, 1);
        OLED_WriteChstr1212(54,13, ",", 1);
        OLED_Display0612Num(60,13, Usartdata.y, 3, 1);
        
        // 显示误差
        CameraData_t* cam_data = Camera_GetData();
        OLED_Display0612Num(30,26, cam_data->err_x, 3, 1);
        OLED_WriteChstr1212(54,26, ",", 1);
        OLED_Display0612Num(60,26, cam_data->err_y, 3, 1);
        
        // 显示角度
        float pitch_angle, yaw_angle;
        Get_Target_Angles(&pitch_angle, &yaw_angle);
        OLED_Display0612Num1dot(30,39, (int16_t)(pitch_angle * 10), 1);
        OLED_WriteChstr1212(54,39, ",", 1);
        OLED_Display0612Num1dot(60,39, (int16_t)(yaw_angle * 10), 1);
        
        // 显示状态
        OLED_WriteChstr1212(30,52, "TRACK", 1);
    }
    else
    {
        OLED_WriteChstr1212(30,13, "---,---", 1);
        OLED_WriteChstr1212(30,26, "---,---", 1);
        OLED_WriteChstr1212(30,39, "---,---", 1);
        OLED_WriteChstr1212(30,52, "WAIT", 1);
    }
    
    // 按键退出
    if(Key_C_sta==1)
    {
        WorkMode=0x01;  // 返回平衡模式
        UI_Display_Flag=1;
        Key_C_sta=0;
    }
}
