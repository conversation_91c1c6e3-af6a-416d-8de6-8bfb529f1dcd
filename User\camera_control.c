#include "camera_control.h"
#include "data_transfer.h"
#include "control.h"
#include "motor.h"
#include "anbt_dmp_mpu6050.h"
#include "math.h"
#include "usart.h"

/* ======================== 全局变量定义 ======================== */

// 接收缓冲区
static uint8_t rx_buffer[RX_BUFFER_SIZE];
static uint8_t rx_index = 0;

// 相机数据
static CameraData_t camera_data = {0};
_DATAPoint Usartdata = {0,};
int16_t usart_point[1][2] = {{0,},};

// 目标角度
float target_angle[2] = {0.0f, 0.0f};  // [pitch, yaw]

// 数据超时保护
static uint32_t last_camera_data_time = 0;

/* ======================== 串口中断处理 ======================== */

/**
 * @brief 串口3中断处理函数
 */
void USART3_IRQHandler(void)
{
    if(USART_GetITStatus(USART3, USART_IT_RXNE) != RESET)
    {
        uint8_t received_byte = USART_ReceiveData(USART3);

        // 查找帧头 0xFF 0xF8
        if(rx_index == 0 && received_byte == 0xFF)
        {
            rx_buffer[rx_index] = received_byte;
            rx_index = 1;
        }
        else if(rx_index == 1 && received_byte == 0xF8)
        {
            rx_buffer[rx_index] = received_byte;
            rx_index = 2;
        }
        else if(rx_index >= 2 && rx_index < PACKET_SIZE - 1)
        {
            rx_buffer[rx_index] = received_byte;
            rx_index++;
        }
        else if(rx_index == PACKET_SIZE - 1 && received_byte == 0xFE)
        {
            rx_buffer[rx_index] = received_byte;
            // 接收到完整数据包，解析数据
            Camera_ParseData(rx_buffer);
            rx_index = 0;
        }
        else
        {
            // 数据错误，重新开始
            rx_index = 0;
        }

        USART_ClearITPendingBit(USART3, USART_IT_RXNE);
    }
}

/* ======================== 数据解析处理 ======================== */

/**
 * @brief 解析相机数据包
 * @param data 接收到的数据包
 */
void Camera_ParseData(uint8_t *data)
{
    // 检查帧头和帧尾
    if(data[0] == 0xFF && data[1] == 0xF8 && data[9] == 0xFE)
    {
        // 解析坐标 (小端序)
        camera_data.center_x = data[2] | (data[3] << 8);
        camera_data.center_y = data[4] | (data[5] << 8);

        // 解析误差值 (有符号字节)
        camera_data.err_x = (int8_t)data[6];
        camera_data.err_y = (int8_t)data[7];

        // 检查固定标识位
        if(data[8] == 0x01)
        {
            camera_data.valid = 1;
            last_camera_data_time = time_tick;  // 更新数据时间戳
        }
        else
        {
            camera_data.valid = 0;
        }
    }
}

/**
 * @brief 数据转化处理
 */
void vData_Get(void)
{
    // 如果有新的有效相机数据
    if(camera_data.valid)
    {
        // 将相机数据转换为Usartdata格式
        Usartdata.x = camera_data.center_x;
        Usartdata.y = camera_data.center_y;
        Usartdata.num = 0;  // 单点数据，编号为0

        // 打印接收到的数据（调试用）
        #ifdef DEBUG_CAMERA_DATA
        printf("Camera: (%d, %d), Error: X=%d, Y=%d\r\n",
               Usartdata.x, Usartdata.y,
               camera_data.err_x, camera_data.err_y);
        #endif

        // 清除有效标志
        camera_data.valid = 0;
    }
}

/* ======================== 坐标转换算法 ======================== */

/**
 * @brief 图像坐标转换为云台角度
 * @param Image: 图像坐标数组 [x,y]
 * @param cnt: 坐标点数量
 * @retval None
 * @note 将图像处理坐标转换为云台目标角度
 */
void vImage_To_Gimbal_Angle(int16_t Image[][2], uint8_t cnt)
{
    float Alpha_x, Alpha_y;
    
    if(cnt > 0)  // 只处理第一个点
    {
        // 计算相对于屏幕中心的偏移
        int16_t offset_x = Image[0][0] - SCREEN_CENTER_X;
        int16_t offset_y = Image[0][1] - SCREEN_CENTER_Y;
        
        // X轴坐标转换：像素->角度
        Alpha_x = atan2(offset_x * ImageToActual, Distance);
        target_angle[1] = Alpha * Alpha_x * 180.0f / 3.14159f;  // YAW角度(度)
        
        // Y轴坐标转换：像素->角度  
        Alpha_y = atan2(offset_y * ImageToActual, Distance);
        target_angle[0] = -Alpha * Alpha_y * 180.0f / 3.14159f;  // PITCH角度(度)，负号校正方向
        
        // 限制角度范围
        if(target_angle[0] > MAX_PITCH_ANGLE) target_angle[0] = MAX_PITCH_ANGLE;
        if(target_angle[0] < MIN_PITCH_ANGLE) target_angle[0] = MIN_PITCH_ANGLE;
        if(target_angle[1] > MAX_YAW_ANGLE) target_angle[1] = MAX_YAW_ANGLE;
        if(target_angle[1] < MIN_YAW_ANGLE) target_angle[1] = MIN_YAW_ANGLE;
    }
}

/* ======================== 控制算法 ======================== */

/**
 * @brief 使用图像坐标进行云台控制
 * @param pit_now: 当前俯仰角
 * @param target_x: 目标X坐标
 * @param target_y: 目标Y坐标
 * @retval None
 */
void CONTROL_Image_Target(float pit_now, int16_t target_x, int16_t target_y)
{
    // 检查数据超时
    if(time_tick - last_camera_data_time > 100)  // 100 * 5ms = 500ms超时
    {
        // 数据超时，保持当前位置
        CONTROL(pit_now, 0, 0);
        return;
    }
    
    // 将单点坐标转换为数组格式
    int16_t image_coords[1][2] = {{target_x, target_y}};
    
    // 转换为目标角度
    vImage_To_Gimbal_Angle(image_coords, 1);
    
    // PITCH轴位置控制
    PID_PIT.Error = target_angle[0] - pit_now;
    PID_PIT.pout = PID_PIT.Pdat * PID_PIT.Error;
    PID_PIT.iout += PID_PIT.Idat * PID_PIT.Error * Banlan_Xishu;
    
    pit_gyro_now = number_to_dps(MPU6050_gyro[1]);
    PID_PIT.dout = PID_PIT.Ddat * pit_gyro_now * Banlan_Xishu;
    PID_PIT.OUT = PID_PIT.pout + PID_PIT.iout + PID_PIT.dout;
    
    // YAW轴角速度控制（将角度转换为角速度目标）
    float yaw_speed_target = target_angle[1] * YAW_SPEED_FACTOR;
    
    yaw_gyro_now = number_to_dps(MPU6050_gyro[2]);
    yaw_gyro_now = yaw_gyro_now*0.6f+yaw_gyro_last1*0.3f+yaw_gyro_last0*0.1f;
    yaw_gyro_last0=yaw_gyro_last1;	
    yaw_gyro_last1=yaw_gyro_now;
    
    PID_YAW.Error = yaw_speed_target - yaw_gyro_now;
    PID_YAW.pout = PID_YAW.Pdat * PID_YAW.Error;
    PID_YAW.iout += PID_YAW.Idat * PID_YAW.Error;
    if(PID_YAW.iout>2000.0)PID_YAW.iout=2000.0;
    if(PID_YAW.iout<-2000.0)PID_YAW.iout=-2000.0;
    PID_YAW.dout = PID_YAW.Ddat * (PID_YAW.Error-yaw_gyro_last);
    yaw_gyro_last=PID_YAW.Error;
    PID_YAW.OUT = PID_YAW.pout + PID_YAW.iout + PID_YAW.dout;
    
    // 电机输出
    MotorL_PWM = PID_PIT.OUT; 
    if(MotorL_PWM>4499)MotorL_PWM=4499;
    if(MotorL_PWM<-4999)MotorL_PWM=-4499;
    
    MotorR_PWM = PID_YAW.OUT; 
    if(MotorR_PWM>4499)MotorR_PWM=4499;
    if(MotorR_PWM<-4999)MotorR_PWM=-4499;
    
    if(ARMED)
    {
        Set_Motor(MotorL_PWM, MotorR_PWM);
    }
    else
    {
        PID_Speed.iout=0;PID_PIT.iout=0;PID_YAW.iout=0;
        Set_Motor(0,0);
    }
}

/* ======================== 辅助函数 ======================== */

/**
 * @brief 获取最新的相机数据
 * @return 相机数据结构体指针
 */
CameraData_t* Camera_GetData(void)
{
    return &camera_data;
}

/**
 * @brief 检查是否有新的有效数据
 * @return 1-有新数据, 0-无新数据
 */
uint8_t Camera_HasNewData(void)
{
    // 检查数据有效性和超时
    if(Usartdata.x != 0 && Usartdata.y != 0 && 
       (time_tick - last_camera_data_time) < 100)
    {
        return 1;
    }
    return 0;
}

/**
 * @brief 检查相机数据是否有效
 * @return 1-有效, 0-无效
 */
uint8_t Camera_IsDataValid(void)
{
    return (Usartdata.x > 0 && Usartdata.x < 640 && 
            Usartdata.y > 0 && Usartdata.y < 480);
}

/**
 * @brief 重置接收状态
 */
void reset_receive_state(void)
{
    rx_index = 0;
    camera_data.valid = 0;
    last_camera_data_time = 0;
}

/**
 * @brief 获取当前目标角度（用于调试）
 * @param pitch_angle: 返回PITCH目标角度
 * @param yaw_angle: 返回YAW目标角度
 */
void Get_Target_Angles(float *pitch_angle, float *yaw_angle)
{
    *pitch_angle = target_angle[0];
    *yaw_angle = target_angle[1];
}

/**
 * @brief 打印调试信息
 */
void Print_Debug_Info(int16_t x, int16_t y)
{
    printf("Target: (%d,%d) -> Angles: P=%.2f°, Y=%.2f°\r\n", 
           x, y, target_angle[0], target_angle[1]);
}
