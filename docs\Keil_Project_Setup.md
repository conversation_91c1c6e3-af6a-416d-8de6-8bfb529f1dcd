# Keil工程配置指南

## 📋 工程切换步骤

### 第一步：运行切换脚本

1. **双击运行** `switch_to_camera_system.bat`
2. **检查输出**，确保所有文件都正确替换和添加
3. **备份确认**，原有文件已保存到 `backup_original` 目录

### 第二步：Keil工程配置

#### 1. **打开Keil项目**
- 双击 `Main\TEST.uvprojx` 打开项目
- 等待Keil完全加载项目

#### 2. **添加新源文件**

在Project窗口中：

```
📁 Target 1
├── 📁 STARTUP
├── 📁 FWLIB
├── 📁 HARDWARE
├── 📁 SYSTEM
├── 📁 DMP
├── 📁 USER
│   ├── 📄 control.c
│   ├── 📄 data_transfer.c      ← 已替换
│   ├── 📄 UI.c
│   ├── 📄 camera_control.c     ← 需要添加
│   └── 📄 UI_enhanced.c        ← 需要添加
└── 📁 MAIN
    └── 📄 Main.c               ← 已替换
```

**添加步骤：**
1. 右键点击 `USER` 组
2. 选择 `Add Files to Group 'USER'...`
3. 浏览到 `User` 目录
4. 选择以下文件：
   - `camera_control.c`
   - `UI_enhanced.c`
5. 点击 `Add` 添加文件
6. 点击 `Close` 关闭对话框

#### 3. **检查包含路径**

1. 右键项目名称 → `Options for Target...`
2. 切换到 `C/C++` 标签
3. 在 `Include Paths` 中确保包含以下路径：

```
.\User
.\Hardware
.\SYSTEM\sys
.\SYSTEM\delay
.\SYSTEM\usart
.\DMP
.\FWLIB\inc
.\STARTUP
```

如果缺少路径，点击 `...` 按钮添加。

#### 4. **编译设置检查**

在 `C/C++` 标签中：
- **Optimization**: `-O1` 或 `-O2`
- **Define**: 可以添加以下宏定义（可选）
  ```
  USE_STDPERIPH_DRIVER
  STM32F10X_MD
  DEBUG_CAMERA_DATA    // 启用相机调试输出
  ```

#### 5. **链接器设置检查**

切换到 `Linker` 标签：
- **Use Memory Layout from Target Dialog**: 勾选
- **Scatter File**: 使用默认设置

### 第三步：编译项目

#### 1. **清理项目**
- 菜单栏：`Project` → `Clean Targets`
- 或按快捷键 `Alt + F7`

#### 2. **重新编译**
- 菜单栏：`Project` → `Rebuild all target files`
- 或按快捷键 `F7`

#### 3. **检查编译结果**

**成功编译应该显示：**
```
Build target 'Target 1'
compiling camera_control.c...
compiling UI_enhanced.c...
compiling Main.c...
compiling data_transfer.c...
...
linking...
Program Size: Code=xxxxx RO-data=xxxx RW-data=xxx ZI-data=xxxx
".\Obj\TEST.axf" - 0 Error(s), 0 Warning(s).
Build Time Elapsed:  00:00:xx
```

**如果有错误：**
- 检查文件路径是否正确
- 确认所有头文件都能找到
- 检查语法错误

### 第四步：下载和调试设置

#### 1. **调试器配置**
1. 右键项目 → `Options for Target...`
2. 切换到 `Debug` 标签
3. 选择你的调试器（如 ST-Link）
4. 点击 `Settings` 配置调试器参数

#### 2. **Flash下载配置**
1. 切换到 `Utilities` 标签
2. 勾选 `Use Target Driver for Flash Programming`
3. 选择对应的Flash算法

### 第五步：功能验证

#### 1. **编译验证**
- 确保编译无错误无警告
- 检查程序大小是否合理

#### 2. **下载验证**
- 连接调试器和目标板
- 点击下载按钮或按 `F8`
- 确认下载成功

#### 3. **运行验证**
- 系统上电后检查OLED显示
- 验证串口通信功能
- 测试相机数据接收

## ⚠️ 常见问题解决

### 编译错误

#### 1. **找不到头文件**
```
Error: #5: cannot open source input file "camera_control.h"
```
**解决方法：**
- 检查 `Include Paths` 是否包含 `.\User`
- 确认 `camera_control.h` 文件存在

#### 2. **函数未定义**
```
Error: L6218E: Undefined symbol Camera_HasNewData
```
**解决方法：**
- 确认 `camera_control.c` 已添加到项目
- 检查函数名拼写是否正确

#### 3. **重复定义**
```
Error: L6200E: Symbol multiply defined
```
**解决方法：**
- 检查是否有重复的全局变量定义
- 确认头文件包含保护宏正确

### 链接错误

#### 1. **内存不足**
```
Error: L6220E: Load region LR_IROM1 size exceeds limit
```
**解决方法：**
- 检查程序大小是否超出Flash容量
- 优化编译选项
- 移除不必要的调试代码

#### 2. **栈溢出**
```
Error: L6915E: Library reports error: __use_no_semihosting_swi
```
**解决方法：**
- 增加栈大小设置
- 检查递归调用
- 优化局部变量使用

### 运行时问题

#### 1. **系统不响应**
- 检查时钟配置是否正确
- 验证中断向量表设置
- 检查看门狗配置

#### 2. **串口无数据**
- 确认波特率设置 (9600)
- 检查引脚配置 (PB10/PB11)
- 验证中断使能设置

#### 3. **OLED显示异常**
- 检查I2C通信是否正常
- 确认显示刷新频率
- 验证字符编码设置

## 📊 性能优化建议

### 1. **编译优化**
- 使用 `-O2` 优化级别
- 启用 `Link-Time Optimization`
- 移除未使用的函数

### 2. **内存优化**
- 合理使用栈和堆
- 优化全局变量使用
- 使用const修饰常量数据

### 3. **实时性优化**
- 优化中断处理时间
- 合理设置任务优先级
- 减少不必要的计算

## 🔧 调试技巧

### 1. **使用调试输出**
```c
#define DEBUG_CAMERA_DATA    // 启用相机调试
printf("Camera data: x=%d, y=%d\r\n", x, y);
```

### 2. **使用断点调试**
- 在关键函数设置断点
- 观察变量值变化
- 单步执行验证逻辑

### 3. **使用逻辑分析仪**
- 监控串口通信
- 分析PWM输出波形
- 检查时序关系

这个配置指南涵盖了从工程切换到调试验证的完整流程，按照步骤操作即可成功切换到新的相机云台系统。
