#include "control.h"
#include "delay.h"
#include "motor.h"
#include "math.h"
#include "stmflash.h"
#include "encoder.h"
#include "anbt_dmp_mpu6050.h"
#include "data_transfer.h"

int MotorL_PWM=0;	//左电机PWM
int MotorR_PWM=0;	//右电机PWM

u8 ARMED=0;
PID PID_Speed; 	//定义速度控制PID
PID PID_PIT; 		//定义平衡控制PID
PID PID_YAW; 		//定义转向控制PID
short int Front_SafeAngle=0;	//前倾安全角度
short int Back_SafeAngle=0;		//后倾安全角度

float yaw_gyro_last=0;
float pit_gyro_now=0;
float yaw_gyro_now=0.0f,yaw_gyro_last0=0.0f,yaw_gyro_last1=0.0f;
unsigned char SpeedControl_stik=0;

unsigned char PickUp_Flag=0;			//拿起标志位


int Speed_tar_Last=0;
unsigned char Speed_ILimtFlag=0;
unsigned char FullSpeed_CK=0;

float Speed_Xishu=1.0;
float Banlan_Xishu=1.0;
int Encode0=0,Encode1=0,Encode2=0;
float PID_Speed_OUT0=0.0f,PID_Speed_OUT1=0.0f;

void PID_Init(void)
{	
	Flash_Read_PID();
	Flash_Read_SafeAngle();
	if(Front_SafeAngle>80 || Front_SafeAngle<10) Front_SafeAngle=45;
	if(Back_SafeAngle>-10 || Back_SafeAngle<-80) Back_SafeAngle=-45;
	Flash_Save_SafeAngle();
	PID_Speed.OUT=0;
	Encode0=0;Encode1=0;Encode2=0;
	yaw_gyro_now=0.0f;yaw_gyro_last0=0.0f;yaw_gyro_last1=0.0f;
	PID_Speed_OUT0=0.0f;PID_Speed_OUT1=0.0f;	
}

//20190826 调的参数 ,效果可以，静止站立比较稳，行走流畅，回位准确，自弹起较好。加速过猛
//速度P:60 ,I:47 ,D:71
//平衡P:379,I:0 ,	D:184
//方向P:16  ,I:0 ,	D:0


void CONTROL(float pit_now, int Speed_tar,s16 yaw_gyro_tar)
{	

	PID_PIT.Error=0-pit_now;
/////////计算P数值/////////////	
	PID_PIT.pout = PID_PIT.Pdat * PID_PIT.Error;
/////////计算I数值/////////////	
	PID_PIT.iout += PID_PIT.Idat * PID_PIT.Error * Banlan_Xishu;//输出的I值
		
	pit_gyro_now= number_to_dps(MPU6050_gyro[1]);//(PID_PIT.Error-PID_PIT.Last_Error)*20;	  //PIT轴的当前角速度
	PID_PIT.Last_Error=PID_PIT.Error;
/////////计算D数值/////////////	
	PID_PIT.dout = PID_PIT.Ddat * pit_gyro_now * Banlan_Xishu;

	PID_PIT.OUT = PID_PIT.pout + PID_PIT.iout + PID_PIT.dout;////PID值相加

//////////////////////方向控制//////////////////////////////
	yaw_gyro_now= number_to_dps(MPU6050_gyro[2]);			  //YAW轴的当前角速度
	yaw_gyro_now = yaw_gyro_now*0.6f+yaw_gyro_last1*0.3f+yaw_gyro_last0*0.1f; 
	yaw_gyro_last0=yaw_gyro_last1;	
	yaw_gyro_last1=yaw_gyro_now;
	
	PID_YAW.Error = yaw_gyro_tar-yaw_gyro_now ;	//YAW角速度差值（yaw_gyro_tar/12 = 0-30）
/////////计算P数值/////////////	
	PID_YAW.pout = PID_YAW.Pdat * PID_YAW.Error;//P值*误差
/////////计算I数值/////////////	
	PID_YAW.iout += PID_YAW.Idat * PID_YAW.Error;
	if(PID_YAW.iout>2000.0)PID_YAW.iout=2000.0;
	if(PID_YAW.iout<-2000.0)PID_YAW.iout=-2000.0;
/////////计算D数值/////////////		
	PID_YAW.dout = PID_YAW.Ddat * (PID_YAW.Error-yaw_gyro_last);//D值*误差的变化 防止YAW轴转动
	yaw_gyro_last=PID_YAW.Error;	// 记录上一次角速度误差	
	
	PID_YAW.OUT = PID_YAW.pout + PID_YAW.iout + PID_YAW.dout;////PID值相加

	MotorL_PWM = PID_PIT.OUT; 
	if(MotorL_PWM>4499)MotorL_PWM=4499;
	if(MotorL_PWM<-4999)MotorL_PWM=-4499;
	
	MotorR_PWM = PID_YAW.OUT; 
	if(MotorR_PWM>4499)MotorR_PWM=4499;
	if(MotorR_PWM<-4999)MotorR_PWM=-4499;
	
	if(ARMED  )//&& (pit_now>(float)Back_SafeAngle) && (pit_now<(float)Front_SafeAngle )
	{
		Set_Motor(MotorL_PWM,MotorR_PWM);//电机PWM输出
	}
	else
	{
		PID_Speed.iout=0;PID_PIT.iout=0;PID_YAW.iout=0;
		Set_Motor(0,0);
	}
	
}



