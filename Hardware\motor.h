#ifndef __MOTOR_H__
#define __MOTOR_H__

#include "sys.h"

#define ROLL_MOTOR_PWM	TIM1->CCR1
#define ROLL_MOTOR_DIR	PBout(13)
#define ROLL_MOTOR_STOP	PBout(12)
#define ROLL_MOTOR_KEY 	PBin(3)

#define YAW_MOTOR_PWM		TIM1->CCR4
#define YAW_MOTOR_DIR		PBout(14)
#define YAW_MOTOR_STOP	PBout(15)
#define YAW_MOTOR_KEY 	PAin(15)


void Motor_Init(void);//电机PWM初始化并设置电机的刷新速率50-499
void Set_Motor(int M1,int M2);

#endif


